"""
Ultimate Dashboard Views
Provides the most comprehensive dashboard views with advanced filtering,
role-based customization, and real-time data updates.
"""

import json
import logging
from datetime import datetime, timedelta
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from django.utils import timezone
from django.views import View
from django.views.decorators.cache import cache_page
from django.views.decorators.http import require_http_methods
from django.core.cache import cache

from ..services.ultimate_dashboard_service import UltimateDashboardService

logger = logging.getLogger(__name__)


class UltimateDashboardView(LoginRequiredMixin, View):
    """
    Ultimate comprehensive dashboard view with role-based customization
    """
    template_name = 'ultimate_dashboard.html'
    
    def get(self, request):
        """
        Render the ultimate dashboard with comprehensive data
        """
        try:
            # Get parameters
            date_range = request.GET.get('range', '30')
            start_date_param = request.GET.get('start_date')
            end_date_param = request.GET.get('end_date')
            refresh = request.GET.get('refresh', 'false').lower() == 'true'

            # Handle custom date range
            if date_range == 'custom' and start_date_param and end_date_param:
                try:
                    from datetime import datetime
                    start_date = datetime.strptime(start_date_param, '%Y-%m-%d').date()
                    end_date = datetime.strptime(end_date_param, '%Y-%m-%d').date()

                    # Calculate days between dates
                    days_in_range = (end_date - start_date).days + 1

                    # Validate date range
                    if days_in_range <= 0 or days_in_range > 3650:  # Max 10 years
                        raise ValueError("Invalid date range")

                except ValueError:
                    # Fall back to default if invalid dates
                    days_in_range = 30
                    date_range = '30'
                    start_date_param = None
                    end_date_param = None
            else:
                # Validate predefined date range
                try:
                    days_in_range = int(date_range)
                    if days_in_range not in [7, 30, 90, 180, 365, 730]:
                        days_in_range = 30
                        date_range = '30'
                except ValueError:
                    days_in_range = 30
                    date_range = '30'

                # Clear custom date params for predefined ranges
                start_date_param = None
                end_date_param = None
            
            # Clear cache if refresh requested
            if refresh:
                cache_key = f"ultimate_dashboard_{days_in_range}_{timezone.now().date()}_comprehensive"
                cache.delete(cache_key)
            
            # Initialize service with custom dates if provided
            if date_range == 'custom' and start_date_param and end_date_param:
                dashboard_service = UltimateDashboardService(
                    date_range_days=days_in_range,
                    user=request.user,
                    start_date=start_date,
                    end_date=end_date
                )
            else:
                dashboard_service = UltimateDashboardService(
                    date_range_days=days_in_range,
                    user=request.user
                )

            # Get comprehensive dashboard data
            dashboard_data = dashboard_service.get_comprehensive_dashboard_data()

            # Prepare date context
            if date_range == 'custom' and start_date_param and end_date_param:
                context_start_date = start_date
                context_end_date = end_date
            else:
                from django.utils import timezone
                context_end_date = timezone.now().date()
                context_start_date = context_end_date - timedelta(days=days_in_range)

            # Add request-specific context
            context = {
                **dashboard_data,
                'selected_range': date_range,
                'start_date': context_start_date,
                'end_date': context_end_date,
                'user': request.user,
                'is_refresh': refresh,
                'available_ranges': [
                    {'value': '7', 'label': 'Last 7 Days'},
                    {'value': '30', 'label': 'Last 30 Days'},
                    {'value': '90', 'label': 'Last 3 Months'},
                    {'value': '180', 'label': 'Last 6 Months'},
                    {'value': '365', 'label': 'Last Year'},
                    {'value': '730', 'label': 'Last 2 Years'},
                ],
                'dashboard_title': self._get_dashboard_title(request.user),
                'show_advanced_features': self._should_show_advanced_features(request.user),
            }
            
            return render(request, self.template_name, context)
            
        except Exception as e:
            logger.error(f"Error rendering ultimate dashboard: {e}")
            
            # Fallback context
            error_context = {
                'error': True,
                'error_message': 'Unable to load dashboard data. Please try again.',
                'selected_range': date_range,
                'user': request.user,
                'dashboard_title': 'Dashboard',
                'show_advanced_features': False,
            }
            
            return render(request, self.template_name, error_context)
    
    def _get_dashboard_title(self, user) -> str:
        """Get personalized dashboard title based on user role"""
        if user.is_superuser:
            return 'Executive Dashboard'
        elif user.user_type == 2:  # Dentist
            return f'Welcome, Dr. {user.last_name}'
        elif hasattr(user, 'departments') and user.departments.filter(is_manager=True).exists():
            return 'Management Dashboard'
        else:
            return 'Dashboard'
    
    def _should_show_advanced_features(self, user) -> bool:
        """Determine if advanced features should be shown"""
        return (
            user.is_superuser or 
            (hasattr(user, 'departments') and user.departments.filter(is_manager=True).exists())
        )


@login_required
def ultimate_home_view(request):
    """
    Function-based view for ultimate dashboard (alternative to class-based)
    """
    try:
        # Get parameters
        date_range = request.GET.get('range', '30')
        
        # Validate date range
        try:
            days_in_range = int(date_range)
            if days_in_range not in [7, 30, 90, 180, 365, 730]:
                days_in_range = 30
                date_range = '30'
        except ValueError:
            days_in_range = 30
            date_range = '30'
        
        # Initialize service
        dashboard_service = UltimateDashboardService(
            date_range_days=days_in_range,
            user=request.user
        )
        
        # Get comprehensive dashboard data
        dashboard_data = dashboard_service.get_comprehensive_dashboard_data()
        
        # Prepare context
        context = {
            **dashboard_data,
            'selected_range': date_range,
            'user': request.user,
            'page_title': 'Ultimate Dashboard',
        }
        
        return render(request, 'ultimate_dashboard.html', context)
        
    except Exception as e:
        logger.error(f"Error in ultimate home view: {e}")
        
        # Fallback context
        context = {
            'error': True,
            'error_message': 'Unable to load dashboard data.',
            'selected_range': date_range,
            'user': request.user,
        }
        
        return render(request, 'ultimate_dashboard.html', context)


# API Views for AJAX updates
@login_required
@require_http_methods(["GET"])
def ultimate_dashboard_api(request):
    """
    API endpoint for dashboard data (AJAX updates)
    """
    try:
        date_range = request.GET.get('range', '30')
        
        # Validate date range
        try:
            days_in_range = int(date_range)
            if days_in_range not in [7, 30, 90, 180, 365, 730]:
                days_in_range = 30
        except ValueError:
            days_in_range = 30
        
        # Initialize service
        dashboard_service = UltimateDashboardService(
            date_range_days=days_in_range,
            user=request.user
        )
        
        # Get data
        dashboard_data = dashboard_service.get_comprehensive_dashboard_data()
        
        # Convert datetime objects to strings for JSON serialization
        def serialize_datetime(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        return JsonResponse(dashboard_data, json_dumps_params={'default': serialize_datetime})
        
    except Exception as e:
        logger.error(f"Error in ultimate dashboard API: {e}")
        return JsonResponse({
            'error': True,
            'message': 'Unable to fetch dashboard data'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def financial_overview_api(request):
    """
    API endpoint for financial overview data
    """
    try:
        date_range = int(request.GET.get('range', '30'))
        
        dashboard_service = UltimateDashboardService(
            date_range_days=date_range,
            user=request.user
        )
        
        financial_data = dashboard_service.get_financial_overview()
        
        return JsonResponse(financial_data)
        
    except Exception as e:
        logger.error(f"Error in financial overview API: {e}")
        return JsonResponse({
            'error': True,
            'message': 'Unable to fetch financial data'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def operational_metrics_api(request):
    """
    API endpoint for operational metrics
    """
    try:
        date_range = int(request.GET.get('range', '30'))
        
        dashboard_service = UltimateDashboardService(
            date_range_days=date_range,
            user=request.user
        )
        
        operational_data = dashboard_service.get_operational_metrics()
        
        return JsonResponse(operational_data)
        
    except Exception as e:
        logger.error(f"Error in operational metrics API: {e}")
        return JsonResponse({
            'error': True,
            'message': 'Unable to fetch operational data'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def real_time_alerts_api(request):
    """
    API endpoint for real-time alerts
    """
    try:
        dashboard_service = UltimateDashboardService(user=request.user)
        alerts = dashboard_service.get_real_time_alerts()
        
        return JsonResponse({'alerts': alerts})
        
    except Exception as e:
        logger.error(f"Error in real-time alerts API: {e}")
        return JsonResponse({
            'error': True,
            'message': 'Unable to fetch alerts'
        }, status=500)


@login_required
@require_http_methods(["GET"])
@cache_page(60 * 5)  # Cache for 5 minutes
def chart_data_api(request):
    """
    API endpoint for chart data (cached for performance)
    """
    try:
        date_range = int(request.GET.get('range', '30'))
        
        dashboard_service = UltimateDashboardService(
            date_range_days=date_range,
            user=request.user
        )
        
        chart_data = dashboard_service.get_comprehensive_chart_data()
        
        return JsonResponse(chart_data)
        
    except Exception as e:
        logger.error(f"Error in chart data API: {e}")
        return JsonResponse({
            'error': True,
            'message': 'Unable to fetch chart data'
        }, status=500)


# Export functionality
@login_required
def export_dashboard_data(request):
    """
    Export dashboard data to CSV/Excel
    """
    try:
        format_type = request.GET.get('format', 'csv')
        date_range = int(request.GET.get('range', '30'))
        
        dashboard_service = UltimateDashboardService(
            date_range_days=date_range,
            user=request.user
        )
        
        dashboard_data = dashboard_service.get_comprehensive_dashboard_data()
        
        if format_type == 'csv':
            return _export_to_csv(dashboard_data)
        elif format_type == 'excel':
            return _export_to_excel(dashboard_data)
        else:
            return JsonResponse({'error': 'Unsupported format'}, status=400)
            
    except Exception as e:
        logger.error(f"Error exporting dashboard data: {e}")
        return JsonResponse({
            'error': True,
            'message': 'Unable to export data'
        }, status=500)


def _export_to_csv(data):
    """Export data to CSV format"""
    # Implementation for CSV export
    # This is a placeholder - implement based on your specific needs
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="dashboard_data.csv"'
    return response


def _export_to_excel(data):
    """Export data to Excel format"""
    # Implementation for Excel export
    # This is a placeholder - implement based on your specific needs
    response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename="dashboard_data.xlsx"'
    return response
