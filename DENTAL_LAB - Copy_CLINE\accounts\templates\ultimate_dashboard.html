{% extends "base.html" %}
{% load static humanize mathfilters %}

{% block title %}{{ dashboard_title|default:"Ultimate Dashboard" }} | Dental Lab Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
:root {
    /* Modern Color Palette */
    --primary: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --secondary: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #06b6d4;
    
    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Base Styles */
body {
    background-color: var(--gray-50);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    color: var(--gray-800);
    line-height: 1.6;
}

/* Dashboard Container */
.ultimate-dashboard {
    min-height: 100vh;
    padding: var(--space-6);
}

/* Header Section */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    padding: var(--space-8) var(--space-6);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.header-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: var(--space-2) 0 0 0;
}

.header-controls {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

.date-filter-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.custom-date-range {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-2);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

.date-input {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    min-width: 140px;
}

.date-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.date-input::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
}

.date-separator {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    font-weight: 500;
}

.apply-dates-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.apply-dates-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.period-selector {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
}

.period-selector:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.refresh-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.refresh-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Alert Banner */
.alert-banner {
    background: linear-gradient(90deg, var(--warning) 0%, #f97316 100%);
    color: white;
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    box-shadow: var(--shadow-md);
    animation: slideInDown 0.5s ease-out;
}

.alert-banner.critical {
    background: linear-gradient(90deg, var(--danger) 0%, #dc2626 100%);
}

.alert-banner.info {
    background: linear-gradient(90deg, var(--info) 0%, #0891b2 100%);
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.metric-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
}

.metric-card.success::before {
    background: linear-gradient(90deg, var(--success) 0%, #059669 100%);
}

.metric-card.warning::before {
    background: linear-gradient(90deg, var(--warning) 0%, #d97706 100%);
}

.metric-card.danger::before {
    background: linear-gradient(90deg, var(--danger) 0%, #dc2626 100%);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-4);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

.metric-icon.success {
    background: linear-gradient(135deg, var(--success) 0%, #059669 100%);
}

.metric-icon.warning {
    background: linear-gradient(135deg, var(--warning) 0%, #d97706 100%);
}

.metric-icon.danger {
    background: linear-gradient(135deg, var(--danger) 0%, #dc2626 100%);
}

.metric-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: var(--space-2) 0;
    line-height: 1;
}

.metric-change {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: 0.875rem;
    font-weight: 500;
}

.metric-change.positive {
    color: var(--success);
}

.metric-change.negative {
    color: var(--danger);
}

.metric-change.neutral {
    color: var(--gray-500);
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-8);
    margin-bottom: var(--space-8);
}

/* Chart Container */
.chart-container {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.chart-canvas {
    width: 100%;
    height: 400px;
}

/* Sidebar Widgets */
.sidebar-widgets {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.widget {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--gray-200);
}

.widget-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.widget-content {
    max-height: 300px;
    overflow-y: auto;
}

/* Activity Item */
.activity-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) 0;
    border-bottom: 1px solid var(--gray-100);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-900);
    margin: 0 0 var(--space-1) 0;
}

.activity-description {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin: 0;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--gray-500);
    white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .header-controls {
        width: 100%;
        justify-content: flex-end;
    }

    .date-filter-container {
        width: 100%;
    }

    .custom-date-range {
        flex-wrap: wrap;
        gap: var(--space-2);
    }

    .date-input {
        min-width: 120px;
        flex: 1;
    }
}

@media (max-width: 768px) {
    .ultimate-dashboard {
        padding: var(--space-4);
    }
    
    .dashboard-header {
        padding: var(--space-6) var(--space-4);
    }
    
    .header-title {
        font-size: 2rem;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .metric-value {
        font-size: 2rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--gray-300);
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Animations */
@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Department Cards */
.department-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-4);
}

.department-card {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    border: 1px solid var(--gray-200);
}

.department-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
}

.department-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.department-utilization {
    font-size: 0.875rem;
    font-weight: 600;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
}

.department-utilization.success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.department-utilization.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.department-utilization.danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.department-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-2);
    margin-bottom: var(--space-3);
}

.stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-bottom: var(--space-1);
}

.stat-value {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
}

.progress-bar {
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.progress-fill.success {
    background: var(--success);
}

.progress-fill.warning {
    background: var(--warning);
}

.progress-fill.danger {
    background: var(--danger);
}

/* Workload Items */
.workload-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.workload-item:last-child {
    border-bottom: none;
}

.workload-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-400);
}

.status-indicator.status-pending_acceptance {
    background: var(--warning);
}

.status-indicator.status-in_progress {
    background: var(--primary);
}

.status-indicator.status-quality_check {
    background: var(--info);
}

.status-indicator.status-ready_to_ship {
    background: var(--success);
}

.status-name {
    font-size: 0.875rem;
    color: var(--gray-700);
}

.workload-count {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
}

/* Deadline Items */
.deadline-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3) 0;
    border-bottom: 1px solid var(--gray-100);
}

.deadline-item:last-child {
    border-bottom: none;
}

.deadline-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.deadline-description {
    font-size: 0.75rem;
    color: var(--gray-600);
}

.deadline-days {
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
}

.deadline-days.critical {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.deadline-days.warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.deadline-days.info {
    background: rgba(6, 182, 212, 0.1);
    color: var(--info);
}

/* Performer Items */
.performer-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) 0;
    border-bottom: 1px solid var(--gray-100);
}

.performer-item:last-child {
    border-bottom: none;
}

.performer-rank {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    flex-shrink: 0;
}

.performer-info {
    flex: 1;
    min-width: 0;
}

.performer-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.performer-clinic {
    font-size: 0.75rem;
    color: var(--gray-600);
}

.performer-stats {
    text-align: right;
}

.performer-revenue {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.performer-cases {
    font-size: 0.75rem;
    color: var(--gray-600);
}

/* Insights Section */
.insights-section {
    margin-top: var(--space-8);
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
}

.insight-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.insight-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--gray-200);
}

.insight-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.quality-score {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 700;
    color: white;
}

.quality-score.excellent {
    background: var(--success);
}

.quality-score.good {
    background: var(--primary);
}

.quality-score.fair {
    background: var(--warning);
}

.quality-score.poor {
    background: var(--danger);
}

.quality-stats,
.prediction-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-4);
}

.quality-stat,
.prediction-stat {
    text-align: center;
}

.bottlenecks {
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 1px solid var(--gray-200);
}

.bottlenecks h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--space-3) 0;
}

.bottleneck-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.bottleneck-item:last-child {
    border-bottom: none;
}

.bottleneck-dept {
    font-size: 0.875rem;
    color: var(--gray-700);
}

.bottleneck-util {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--warning);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-sm {
    padding: var(--space-1) var(--space-3);
    font-size: 0.75rem;
}

.btn-outline-secondary {
    border-color: var(--gray-300);
    color: var(--gray-700);
    background: white;
}

.btn-outline-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-outline-light {
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Text Utilities */
.text-primary {
    color: var(--primary) !important;
}

.text-sm {
    font-size: 0.875rem;
}

.text-gray-500 {
    color: var(--gray-500);
}

.ms-auto {
    margin-left: auto;
}
</style>
{% endblock %}

{% block content %}
<div class="ultimate-dashboard">
    <!-- Dashboard Header -->
    <div class="dashboard-header animate-fade-in-up">
        <div class="header-content">
            <div>
                <h1 class="header-title">{{ dashboard_title|default:"Ultimate Dashboard" }}</h1>
                <p class="header-subtitle">
                    {{ period_description|default:"Comprehensive business insights and analytics" }}
                    {% if last_updated %}
                        • Last updated: {{ last_updated|date:"M d, Y g:i A" }}
                    {% endif %}
                </p>
            </div>
            <div class="header-controls">
                <div class="date-filter-container">
                    <!-- Predefined Periods -->
                    <select class="period-selector" id="periodSelector" onchange="handlePeriodChange()">
                        <option value="custom" {% if selected_range == 'custom' %}selected{% endif %}>Custom Range</option>
                        {% for range in available_ranges %}
                            <option value="{{ range.value }}" {% if selected_range == range.value %}selected{% endif %}>
                                {{ range.label }}
                            </option>
                        {% endfor %}
                    </select>

                    <!-- Custom Date Range -->
                    <div class="custom-date-range" id="customDateRange" {% if selected_range != 'custom' %}style="display: none;"{% endif %}>
                        <input type="date" id="startDate" class="date-input"
                               value="{{ start_date|date:'Y-m-d' }}"
                               title="Start Date">
                        <span class="date-separator">to</span>
                        <input type="date" id="endDate" class="date-input"
                               value="{{ end_date|date:'Y-m-d' }}"
                               title="End Date">
                        <button class="apply-dates-btn" onclick="applyCustomDates()" title="Apply Date Range">
                            <i class="bi bi-check"></i>
                        </button>
                    </div>
                </div>

                <button class="refresh-btn" onclick="refreshDashboard()" title="Refresh Data">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Real-time Alerts -->
    {% if real_time_alerts %}
        {% for alert in real_time_alerts %}
            <div class="alert-banner {{ alert.type }} animate-fade-in-up" style="animation-delay: 0.1s;">
                <i class="bi bi-{% if alert.type == 'critical' %}exclamation-triangle-fill{% elif alert.type == 'warning' %}exclamation-circle-fill{% else %}info-circle-fill{% endif %}"></i>
                <div>
                    <strong>{{ alert.title }}</strong>
                    <span>{{ alert.message }}</span>
                </div>
                {% if alert.action_url %}
                    <a href="{{ alert.action_url }}" class="btn btn-sm btn-outline-light ms-auto">
                        View Details
                    </a>
                {% endif %}
            </div>
        {% endfor %}
    {% endif %}

    <!-- Key Metrics Grid -->
    <div class="metrics-grid">
        <!-- Financial Overview -->
        {% if financial_overview %}
            <div class="metric-card success animate-fade-in-up" style="animation-delay: 0.2s;">
                <div class="metric-header">
                    <div>
                        <h3 class="metric-title">Total Revenue</h3>
                        <div class="metric-value">${{ financial_overview.total_invoiced|floatformat:0|intcomma }}</div>
                        <div class="metric-change {% if financial_overview.revenue_growth > 0 %}positive{% elif financial_overview.revenue_growth < 0 %}negative{% else %}neutral{% endif %}">
                            <i class="bi bi-{% if financial_overview.revenue_growth > 0 %}arrow-up{% elif financial_overview.revenue_growth < 0 %}arrow-down{% else %}dash{% endif %}"></i>
                            {{ financial_overview.revenue_growth|floatformat:1 }}%
                        </div>
                    </div>
                    <div class="metric-icon success">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                </div>
            </div>

            <div class="metric-card warning animate-fade-in-up" style="animation-delay: 0.3s;">
                <div class="metric-header">
                    <div>
                        <h3 class="metric-title">Outstanding</h3>
                        <div class="metric-value">${{ financial_overview.outstanding_amount|floatformat:0|intcomma }}</div>
                        <div class="metric-change neutral">
                            {{ financial_overview.outstanding_count }} invoice{{ financial_overview.outstanding_count|pluralize }}
                        </div>
                    </div>
                    <div class="metric-icon warning">
                        <i class="bi bi-clock-history"></i>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Operational Metrics -->
        {% if operational_metrics %}
            <div class="metric-card animate-fade-in-up" style="animation-delay: 0.4s;">
                <div class="metric-header">
                    <div>
                        <h3 class="metric-title">Total Cases</h3>
                        <div class="metric-value">{{ operational_metrics.total_cases|intcomma }}</div>
                        <div class="metric-change neutral">
                            {{ operational_metrics.cases_today }} today
                        </div>
                    </div>
                    <div class="metric-icon">
                        <i class="bi bi-briefcase"></i>
                    </div>
                </div>
            </div>

            <div class="metric-card {% if operational_metrics.overdue_cases > 0 %}danger{% else %}success{% endif %} animate-fade-in-up" style="animation-delay: 0.5s;">
                <div class="metric-header">
                    <div>
                        <h3 class="metric-title">On-Time Rate</h3>
                        <div class="metric-value">{{ operational_metrics.on_time_delivery_rate|floatformat:1 }}%</div>
                        <div class="metric-change {% if operational_metrics.overdue_cases > 0 %}negative{% else %}positive{% endif %}">
                            {{ operational_metrics.overdue_cases }} overdue
                        </div>
                    </div>
                    <div class="metric-icon {% if operational_metrics.overdue_cases > 0 %}danger{% else %}success{% endif %}">
                        <i class="bi bi-{% if operational_metrics.overdue_cases > 0 %}exclamation-triangle{% else %}check-circle{% endif %}"></i>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Main Content Grid -->
    <div class="content-grid">
        <!-- Charts Section -->
        <div class="charts-section">
            <!-- Revenue Trends Chart -->
            <div class="chart-container animate-fade-in-up" style="animation-delay: 0.6s;">
                <div class="chart-header">
                    <h3 class="chart-title">Revenue & Case Trends</h3>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-secondary" onclick="toggleChartType('revenue')">
                            <i class="bi bi-bar-chart"></i>
                        </button>
                    </div>
                </div>
                <canvas id="revenueTrendsChart" class="chart-canvas"></canvas>
            </div>

            <!-- Department Performance -->
            {% if performance_analytics.department_performance %}
                <div class="chart-container animate-fade-in-up" style="animation-delay: 0.7s; margin-top: var(--space-6);">
                    <div class="chart-header">
                        <h3 class="chart-title">Department Performance</h3>
                    </div>
                    <div class="department-grid">
                        {% for dept in performance_analytics.department_performance %}
                            <div class="department-card">
                                <div class="department-header">
                                    <h4>{{ dept.name }}</h4>
                                    <span class="department-utilization {% if dept.capacity_utilization > 90 %}danger{% elif dept.capacity_utilization > 75 %}warning{% else %}success{% endif %}">
                                        {{ dept.capacity_utilization|floatformat:1 }}%
                                    </span>
                                </div>
                                <div class="department-stats">
                                    <div class="stat">
                                        <span class="stat-label">Cases Handled</span>
                                        <span class="stat-value">{{ dept.cases_handled }}</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">Completion Rate</span>
                                        <span class="stat-value">{{ dept.completion_rate|floatformat:1 }}%</span>
                                    </div>
                                    <div class="stat">
                                        <span class="stat-label">Avg. Completion</span>
                                        <span class="stat-value">{{ dept.avg_completion_days|floatformat:1 }} days</span>
                                    </div>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-fill {% if dept.capacity_utilization > 90 %}danger{% elif dept.capacity_utilization > 75 %}warning{% else %}success{% endif %}"
                                         style="width: {{ dept.capacity_utilization }}%"></div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar Widgets -->
        <div class="sidebar-widgets">
            <!-- Real-time Workload -->
            {% if current_workload %}
                <div class="widget animate-fade-in-up" style="animation-delay: 0.8s;">
                    <div class="widget-header">
                        <h3 class="widget-title">Current Workload</h3>
                        <span class="badge badge-primary">{{ current_workload.total_active_cases }} active</span>
                    </div>
                    <div class="widget-content">
                        {% for status in current_workload.by_status %}
                            <div class="workload-item">
                                <div class="workload-status">
                                    <span class="status-indicator status-{{ status.status }}"></span>
                                    <span class="status-name">{% if status.status == 'pending_acceptance' %}Pending Acceptance{% elif status.status == 'in_progress' %}In Progress{% elif status.status == 'quality_check' %}Quality Check{% elif status.status == 'ready_to_ship' %}Ready to Ship{% else %}{{ status.status|title }}{% endif %}</span>
                                </div>
                                <span class="workload-count">{{ status.count }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Recent Activities -->
            {% if recent_activities %}
                <div class="widget animate-fade-in-up" style="animation-delay: 0.9s;">
                    <div class="widget-header">
                        <h3 class="widget-title">Recent Activities</h3>
                        <a href="#" class="text-primary text-sm">View All</a>
                    </div>
                    <div class="widget-content">
                        {% for activity in recent_activities|slice:":8" %}
                            <div class="activity-item">
                                <div class="activity-icon {{ activity.color }}">
                                    <i class="bi bi-{{ activity.icon }}"></i>
                                </div>
                                <div class="activity-content">
                                    <p class="activity-title">{{ activity.title }}</p>
                                    <p class="activity-description">{{ activity.description }}</p>
                                </div>
                                <div class="activity-time">
                                    {{ activity.timestamp|timesince }} ago
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Upcoming Deadlines -->
            {% if upcoming_deadlines %}
                <div class="widget animate-fade-in-up" style="animation-delay: 1.0s;">
                    <div class="widget-header">
                        <h3 class="widget-title">Upcoming Deadlines</h3>
                        <span class="badge badge-warning">{{ upcoming_deadlines|length }}</span>
                    </div>
                    <div class="widget-content">
                        {% for deadline in upcoming_deadlines|slice:":6" %}
                            <div class="deadline-item">
                                <div class="deadline-info">
                                    <div class="deadline-title">
                                        {% if deadline.type == 'case_deadline' %}
                                            Case #{{ deadline.case_number }}
                                        {% else %}
                                            Invoice #{{ deadline.invoice_number }}
                                        {% endif %}
                                    </div>
                                    <div class="deadline-description">{{ deadline.description }}</div>
                                </div>
                                <div class="deadline-time">
                                    <span class="deadline-days {{ deadline.urgency }}">
                                        {% if deadline.days_remaining == 0 %}
                                            Today
                                        {% elif deadline.days_remaining == 1 %}
                                            Tomorrow
                                        {% else %}
                                            {{ deadline.days_remaining }} days
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Top Performers -->
            {% if performance_analytics.top_dentists %}
                <div class="widget animate-fade-in-up" style="animation-delay: 1.1s;">
                    <div class="widget-header">
                        <h3 class="widget-title">Top Performers</h3>
                        <span class="text-sm text-gray-500">This period</span>
                    </div>
                    <div class="widget-content">
                        {% for dentist in performance_analytics.top_dentists|slice:":5" %}
                            <div class="performer-item">
                                <div class="performer-rank">{{ forloop.counter }}</div>
                                <div class="performer-info">
                                    <div class="performer-name">{{ dentist.name }}</div>
                                    <div class="performer-clinic">{{ dentist.clinic }}</div>
                                </div>
                                <div class="performer-stats">
                                    <div class="performer-revenue">${{ dentist.total_revenue|floatformat:0|intcomma }}</div>
                                    <div class="performer-cases">{{ dentist.case_count }} cases</div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Quality & Predictive Insights -->
    {% if quality_metrics or predictive_insights %}
        <div class="insights-section">
            <div class="insights-grid">
                <!-- Quality Metrics -->
                {% if quality_metrics %}
                    <div class="insight-card animate-fade-in-up" style="animation-delay: 1.2s;">
                        <div class="insight-header">
                            <h3 class="insight-title">Quality Metrics</h3>
                            <div class="quality-score {% if quality_metrics.quality_score >= 90 %}excellent{% elif quality_metrics.quality_score >= 75 %}good{% elif quality_metrics.quality_score >= 60 %}fair{% else %}poor{% endif %}">
                                {{ quality_metrics.quality_score }}
                            </div>
                        </div>
                        <div class="insight-content">
                            <div class="quality-stats">
                                <div class="quality-stat">
                                    <span class="stat-label">First Pass Yield</span>
                                    <span class="stat-value">{{ quality_metrics.first_pass_yield|floatformat:1 }}%</span>
                                </div>
                                <div class="quality-stat">
                                    <span class="stat-label">Rework Rate</span>
                                    <span class="stat-value">{{ quality_metrics.rework_rate|floatformat:1 }}%</span>
                                </div>
                                <div class="quality-stat">
                                    <span class="stat-label">Avg. QC Time</span>
                                    <span class="stat-value">{{ quality_metrics.avg_quality_check_hours|floatformat:1 }}h</span>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Predictive Insights -->
                {% if predictive_insights %}
                    <div class="insight-card animate-fade-in-up" style="animation-delay: 1.3s;">
                        <div class="insight-header">
                            <h3 class="insight-title">Predictive Insights</h3>
                            <div class="growth-indicator {{ predictive_insights.growth_trend }}">
                                <i class="bi bi-{% if predictive_insights.growth_trend == 'strong_growth' %}trending-up{% elif predictive_insights.growth_trend == 'strong_decline' %}trending-down{% else %}dash{% endif %}"></i>
                            </div>
                        </div>
                        <div class="insight-content">
                            <div class="prediction-stats">
                                <div class="prediction-stat">
                                    <span class="stat-label">Next Month Cases</span>
                                    <span class="stat-value">{{ predictive_insights.predicted_next_month_cases }}</span>
                                </div>
                                <div class="prediction-stat">
                                    <span class="stat-label">Predicted Revenue</span>
                                    <span class="stat-value">${{ predictive_insights.predicted_next_month_revenue|floatformat:0|intcomma }}</span>
                                </div>
                            </div>
                            {% if predictive_insights.potential_bottlenecks %}
                                <div class="bottlenecks">
                                    <h4>Potential Bottlenecks</h4>
                                    {% for bottleneck in predictive_insights.potential_bottlenecks|slice:":3" %}
                                        <div class="bottleneck-item">
                                            <span class="bottleneck-dept">{{ bottleneck.department }}</span>
                                            <span class="bottleneck-util">{{ bottleneck.utilization|floatformat:1 }}%</span>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<!-- Chart.js Date Adapter -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<script>
// Global dashboard configuration
const DASHBOARD_CONFIG = {
    apiEndpoints: {
        dashboard: '{% url "accounts:ultimate_dashboard_api" %}',
        financial: '{% url "accounts:financial_overview_api" %}',
        operational: '{% url "accounts:operational_metrics_api" %}',
        alerts: '{% url "accounts:real_time_alerts_api" %}',
        charts: '{% url "accounts:chart_data_api" %}'
    },
    currentRange: '{{ selected_range }}',
    refreshInterval: 300000, // 5 minutes
    user: {
        role: '{{ user_role }}',
        showAdvanced: {{ show_advanced_features|yesno:"true,false" }}
    }
};

// Dashboard state management
class DashboardState {
    constructor() {
        this.isLoading = false;
        this.lastUpdate = new Date();
        this.charts = {};
        this.refreshTimer = null;
    }

    setLoading(loading) {
        this.isLoading = loading;
        document.body.classList.toggle('loading', loading);
    }

    updateLastRefresh() {
        this.lastUpdate = new Date();
    }

    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        this.refreshTimer = setInterval(() => {
            this.refreshAlerts();
        }, DASHBOARD_CONFIG.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    async refreshAlerts() {
        try {
            const response = await fetch(DASHBOARD_CONFIG.apiEndpoints.alerts);
            const data = await response.json();

            if (data.alerts && data.alerts.length > 0) {
                this.updateAlertBanner(data.alerts);
            }
        } catch (error) {
            console.error('Error refreshing alerts:', error);
        }
    }

    updateAlertBanner(alerts) {
        const existingBanners = document.querySelectorAll('.alert-banner');
        const container = document.querySelector('.ultimate-dashboard');

        // Remove existing alert banners
        existingBanners.forEach(banner => banner.remove());

        // Add new alerts
        alerts.forEach((alert, index) => {
            const banner = this.createAlertBanner(alert, index);
            container.insertBefore(banner, container.children[1]);
        });
    }

    createAlertBanner(alert, index) {
        const banner = document.createElement('div');
        banner.className = `alert-banner ${alert.type} animate-fade-in-up`;
        banner.style.animationDelay = `${0.1 + index * 0.1}s`;

        banner.innerHTML = `
            <i class="bi bi-${alert.type === 'critical' ? 'exclamation-triangle-fill' :
                                alert.type === 'warning' ? 'exclamation-circle-fill' : 'info-circle-fill'}"></i>
            <div>
                <strong>${alert.title}</strong>
                <span>${alert.message}</span>
            </div>
            ${alert.action_url ? `<a href="${alert.action_url}" class="btn btn-sm btn-outline-light ms-auto">View Details</a>` : ''}
        `;

        return banner;
    }
}

// Chart management
class ChartManager {
    constructor() {
        this.charts = {};
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            }
        };
    }

    async initializeCharts() {
        try {
            const response = await fetch(`${DASHBOARD_CONFIG.apiEndpoints.charts}?range=${DASHBOARD_CONFIG.currentRange}`);
            const data = await response.json();

            if (data.monthly_revenue_cases) {
                this.createRevenueTrendsChart(data.monthly_revenue_cases);
            }

        } catch (error) {
            console.error('Error initializing charts:', error);
        }
    }

    createRevenueTrendsChart(data) {
        const ctx = document.getElementById('revenueTrendsChart');
        if (!ctx) return;

        const chartData = {
            labels: data.map(item => {
                const date = new Date(item.month + '-01');
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            }),
            datasets: [
                {
                    label: 'Revenue ($)',
                    data: data.map(item => item.revenue),
                    borderColor: 'rgb(37, 99, 235)',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    yAxisID: 'y',
                    tension: 0.4
                },
                {
                    label: 'Cases',
                    data: data.map(item => item.cases),
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    yAxisID: 'y1',
                    tension: 0.4
                }
            ]
        };

        const options = {
            ...this.defaultOptions,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Revenue ($)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Cases'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        };

        if (this.charts.revenueTrends) {
            this.charts.revenueTrends.destroy();
        }

        this.charts.revenueTrends = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: options
        });
    }

    destroyAllCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }
}

// Initialize dashboard
const dashboardState = new DashboardState();
const chartManager = new ChartManager();

// Dashboard functions
function handlePeriodChange() {
    const selector = document.getElementById('periodSelector');
    const customDateRange = document.getElementById('customDateRange');

    if (selector.value === 'custom') {
        customDateRange.style.display = 'flex';
    } else {
        customDateRange.style.display = 'none';
        updateDashboard();
    }
}

function updateDashboard() {
    const selector = document.getElementById('periodSelector');
    const range = selector.value;

    if (range === 'custom') {
        return; // Don't update for custom, wait for date selection
    }

    const url = new URL(window.location);
    url.searchParams.set('range', range);
    url.searchParams.delete('start_date');
    url.searchParams.delete('end_date');
    window.location.href = url.toString();
}

function applyCustomDates() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('Start date cannot be after end date.');
        return;
    }

    const url = new URL(window.location);
    url.searchParams.set('range', 'custom');
    url.searchParams.set('start_date', startDate);
    url.searchParams.set('end_date', endDate);
    window.location.href = url.toString();
}

function setDateRange(days) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
}

function refreshDashboard() {
    const url = new URL(window.location);
    url.searchParams.set('refresh', 'true');
    window.location.href = url.toString();
}

function toggleChartType(chartId) {
    // Implementation for toggling chart types
    console.log('Toggling chart type for:', chartId);
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    chartManager.initializeCharts();

    // Start auto-refresh for alerts
    dashboardState.startAutoRefresh();

    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add click handlers for metric cards
    document.querySelectorAll('.metric-card').forEach(card => {
        card.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    // Add hover effects for interactive elements
    document.querySelectorAll('.activity-item, .deadline-item, .performer-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'var(--gray-50)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    console.log('Ultimate Dashboard initialized successfully');
});

// Cleanup when page unloads
window.addEventListener('beforeunload', function() {
    dashboardState.stopAutoRefresh();
    chartManager.destroyAllCharts();
});

// Handle visibility change (pause/resume auto-refresh)
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        dashboardState.stopAutoRefresh();
    } else {
        dashboardState.startAutoRefresh();
    }
});

// Error handling
window.addEventListener('error', function(e) {
    console.error('Dashboard error:', e.error);
});

// Provide data to global scope for debugging
window.DASHBOARD = {
    state: dashboardState,
    charts: chartManager,
    config: DASHBOARD_CONFIG
};
</script>
{% endblock %}
