{% extends "base.html" %}
{% load i18n %}

{% block title %}{% trans "System Settings" %} - Dental Lab{% endblock %}

{% block extra_css %}
<style>
    .settings-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px 15px 0 0;
        padding: 2rem;
        margin-bottom: 0;
    }

    .settings-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .settings-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #667eea;
    }

    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-secondary {
        border-radius: 8px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .maintenance-warning {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .settings-icon {
        font-size: 3rem;
        opacity: 0.1;
        position: absolute;
        right: 2rem;
        top: 50%;
        transform: translateY(-50%);
    }

    .section-icon {
        color: #667eea;
        margin-right: 0.5rem;
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .alert {
        border-radius: 10px;
        border: none;
        padding: 1rem 1.5rem;
    }

    .breadcrumb-nav {
        background: transparent;
        padding: 1rem 0;
    }

    .breadcrumb {
        background: transparent;
        margin: 0;
    }

    .breadcrumb-item a {
        color: #667eea;
        text-decoration: none;
    }

    .breadcrumb-item.active {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb-nav" aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'home' %}">
                    <i class="fas fa-home me-1"></i>{% trans "Home" %}
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'accounts:user_management' %}">
                    <i class="fas fa-users me-1"></i>{% trans "Administration" %}
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="fas fa-cogs me-1"></i>{% trans "System Settings" %}
            </li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-xl-8 col-lg-10">
            <div class="card settings-card">
                <!-- Header -->
                <div class="settings-header position-relative">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="mb-2">
                                <i class="fas fa-cogs me-3"></i>
                                {% trans "System Settings" %}
                            </h2>
                            <p class="mb-0 opacity-75">
                                {% trans "Configure global system settings and preferences" %}
                            </p>
                        </div>
                    </div>
                    <i class="fas fa-server settings-icon"></i>
                </div>

                <div class="card-body p-4">
                    <!-- Messages -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{% if message.tags == 'success' %}check-circle{% elif message.tags == 'error' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <!-- Maintenance Mode Warning -->
                    <div id="maintenance-warning" class="maintenance-warning d-none">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-3 fs-4"></i>
                            <div>
                                <strong>{% trans "Warning!" %}</strong>
                                <p class="mb-0">{% trans "Enabling maintenance mode will make the system unavailable to regular users." %}</p>
                            </div>
                        </div>
                    </div>

                    <form method="post" class="needs-validation" novalidate id="settings-form">
                        {% csrf_token %}

                        <!-- General Settings Section -->
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-globe section-icon"></i>
                                {% trans "General Configuration" %}
                            </h5>

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-4">
                                        <label for="{{ form.site_name.id_for_label }}" class="form-label">
                                            <i class="fas fa-tag me-2"></i>
                                            {% trans "Site Name" %}
                                        </label>
                                        {{ form.site_name }}
                                        <div class="form-text">
                                            {% trans "This name will appear in the browser title and throughout the application" %}
                                        </div>
                                        {% if form.site_name.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.site_name.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center p-3 bg-light rounded">
                                        <i class="fas fa-eye fs-1 text-muted mb-2"></i>
                                        <p class="small text-muted mb-0">{% trans "Preview" %}</p>
                                        <strong id="site-name-preview">{{ form.site_name.value|default:"Dental Lab" }}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Maintenance Section -->
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-tools section-icon"></i>
                                {% trans "System Maintenance" %}
                            </h5>

                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.maintenance_mode.id_for_label }}" class="form-label">
                                            <i class="fas fa-power-off me-2"></i>
                                            {% trans "Maintenance Mode" %}
                                        </label>
                                        {{ form.maintenance_mode }}
                                        <div class="form-text">
                                            {% trans "When enabled, only administrators can access the system" %}
                                        </div>
                                        {% if form.maintenance_mode.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {{ form.maintenance_mode.errors.0 }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="text-center p-3 rounded" id="maintenance-status">
                                        <i class="fas fa-circle fs-1 mb-2" id="status-indicator"></i>
                                        <p class="mb-0" id="status-text"></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Information Section -->
                        <div class="settings-section">
                            <h5 class="mb-3">
                                <i class="fas fa-info-circle section-icon"></i>
                                {% trans "System Information" %}
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-between py-2 border-bottom">
                                        <span class="text-muted">{% trans "Django Version" %}</span>
                                        <span class="fw-bold">{{ django_version|default:"5.2.3" }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between py-2 border-bottom">
                                        <span class="text-muted">{% trans "Database" %}</span>
                                        <span class="fw-bold">SQLite</span>
                                    </div>
                                    <div class="d-flex justify-content-between py-2">
                                        <span class="text-muted">{% trans "Last Updated" %}</span>
                                        <span class="fw-bold">{{ object.updated_at|default:"Never"|date:"M d, Y H:i" }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="text-center p-3 bg-light rounded">
                                        <i class="fas fa-server fs-1 text-primary mb-2"></i>
                                        <p class="small text-muted mb-0">{% trans "System Status" %}</p>
                                        <span class="badge bg-success fs-6">{% trans "Online" %}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between align-items-center pt-3 border-top">
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    {% trans "Changes will take effect immediately" %}
                                </small>
                            </div>
                            <div class="d-flex gap-2">
                                <a href="{% url 'home' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    {% trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-primary" id="save-btn">
                                    <i class="fas fa-save me-2"></i>
                                    {% trans "Save Settings" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Enhanced Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const siteNameInput = document.getElementById('{{ form.site_name.id_for_label }}');
    const siteNamePreview = document.getElementById('site-name-preview');
    const maintenanceSelect = document.getElementById('{{ form.maintenance_mode.id_for_label }}');
    const maintenanceWarning = document.getElementById('maintenance-warning');
    const statusIndicator = document.getElementById('status-indicator');
    const statusText = document.getElementById('status-text');
    const maintenanceStatus = document.getElementById('maintenance-status');

    // Update site name preview
    if (siteNameInput && siteNamePreview) {
        siteNameInput.addEventListener('input', function() {
            siteNamePreview.textContent = this.value || 'Dental Lab';
        });
    }

    // Update maintenance mode status
    function updateMaintenanceStatus() {
        const isMaintenanceMode = maintenanceSelect.value === 'on';

        if (isMaintenanceMode) {
            maintenanceWarning.classList.remove('d-none');
            statusIndicator.className = 'fas fa-circle fs-1 mb-2 text-warning';
            statusText.innerHTML = '<strong class="text-warning">{% trans "Maintenance Mode" %}</strong>';
            maintenanceStatus.className = 'text-center p-3 rounded bg-warning bg-opacity-10';
        } else {
            maintenanceWarning.classList.add('d-none');
            statusIndicator.className = 'fas fa-circle fs-1 mb-2 text-success';
            statusText.innerHTML = '<strong class="text-success">{% trans "Online" %}</strong>';
            maintenanceStatus.className = 'text-center p-3 rounded bg-success bg-opacity-10';
        }
    }

    // Initialize status
    updateMaintenanceStatus();

    // Listen for changes
    if (maintenanceSelect) {
        maintenanceSelect.addEventListener('change', updateMaintenanceStatus);
    }

    // Form submission with loading state
    const form = document.getElementById('settings-form');
    const saveBtn = document.getElementById('save-btn');

    if (form && saveBtn) {
        form.addEventListener('submit', function() {
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Saving..." %}';
            saveBtn.disabled = true;
        });
    }
});
</script>
{% endblock %}
