INFO 2025-05-23 05:28:03,257 autoreload 35628 32792 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,257 autoreload 35628 32792 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,261 autoreload 552 3416 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,261 autoreload 552 3416 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,284 autoreload 39104 29156 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,284 autoreload 39104 29156 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,288 autoreload 21672 22152 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,288 autoreload 21672 22152 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,391 autoreload 38348 25596 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,391 autoreload 38348 25596 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,450 autoreload 30072 35096 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:03,450 autoreload 30072 35096 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:14,367 autoreload 39104 29156 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:14,367 autoreload 39104 29156 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:14,423 autoreload 30072 35096 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:14,423 autoreload 30072 35096 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:14,944 autoreload 35628 32792 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:14,944 autoreload 35628 32792 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:15,120 autoreload 552 3416 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:15,120 autoreload 552 3416 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:15,157 autoreload 38348 25596 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:15,157 autoreload 38348 25596 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:15,190 autoreload 21672 22152 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:15,190 autoreload 21672 22152 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:15,826 autoreload 37920 11284 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:15,826 autoreload 37920 11284 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,018 autoreload 32640 38168 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,018 autoreload 32640 38168 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,797 autoreload 39472 32736 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,797 autoreload 39472 32736 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,964 autoreload 38836 39060 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,964 autoreload 38836 39060 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,971 autoreload 5336 24448 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,971 autoreload 5336 24448 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,995 autoreload 3220 32720 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:16,995 autoreload 3220 32720 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:41,165 autoreload 3220 32720 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,165 autoreload 3220 32720 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,171 autoreload 38836 39060 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,171 autoreload 38836 39060 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,233 autoreload 39472 32736 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,233 autoreload 39472 32736 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,274 autoreload 32640 38168 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,274 autoreload 32640 38168 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,308 autoreload 5336 24448 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,308 autoreload 5336 24448 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,544 autoreload 37920 11284 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:41,544 autoreload 37920 11284 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:28:43,057 autoreload 28820 28676 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,057 autoreload 28820 28676 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,080 autoreload 20980 38784 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,080 autoreload 20980 38784 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,087 autoreload 7892 27368 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,087 autoreload 7892 27368 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,146 autoreload 1600 7240 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,146 autoreload 1600 7240 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,247 autoreload 28320 29676 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,247 autoreload 28320 29676 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,336 autoreload 29976 34396 Watching for file changes with StatReloader
INFO 2025-05-23 05:28:43,336 autoreload 29976 34396 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:00,225 autoreload 28820 28676 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:00,225 autoreload 28820 28676 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:00,293 autoreload 28320 29676 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:00,293 autoreload 28320 29676 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:00,293 autoreload 29976 34396 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:00,293 autoreload 29976 34396 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:01,347 autoreload 20980 38784 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:01,347 autoreload 20980 38784 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:01,447 autoreload 30476 15372 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:01,447 autoreload 30476 15372 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:01,513 autoreload 7892 27368 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:01,513 autoreload 7892 27368 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:01,522 autoreload 1600 7240 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:01,522 autoreload 1600 7240 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:01,579 autoreload 36292 33296 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:01,579 autoreload 36292 33296 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:01,722 autoreload 29120 8812 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:01,722 autoreload 29120 8812 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:03,102 autoreload 39128 31000 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:03,102 autoreload 39128 31000 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:03,365 autoreload 24656 39172 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:03,365 autoreload 24656 39172 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:03,378 autoreload 8800 28756 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:03,378 autoreload 8800 28756 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:17,580 autoreload 36292 33296 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:17,580 autoreload 36292 33296 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:17,581 autoreload 30476 15372 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:17,581 autoreload 30476 15372 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:17,751 autoreload 29120 8812 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:17,751 autoreload 29120 8812 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:18,568 autoreload 24656 39172 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:18,568 autoreload 24656 39172 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:18,588 autoreload 39128 31000 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:18,588 autoreload 39128 31000 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:18,714 autoreload 8800 28756 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:18,714 autoreload 8800 28756 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:18,961 autoreload 3316 18000 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:18,961 autoreload 3316 18000 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:18,968 autoreload 22168 34988 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:18,968 autoreload 22168 34988 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:19,145 autoreload 37736 27436 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:19,145 autoreload 37736 27436 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:19,980 autoreload 24708 37156 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:19,980 autoreload 24708 37156 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:20,030 autoreload 36796 28520 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:20,030 autoreload 36796 28520 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:20,161 autoreload 31372 33008 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:20,161 autoreload 31372 33008 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:39,268 autoreload 3316 18000 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:39,268 autoreload 3316 18000 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:39,278 autoreload 22168 34988 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:39,278 autoreload 22168 34988 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:39,745 autoreload 37736 27436 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:39,745 autoreload 37736 27436 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:40,286 autoreload 31372 33008 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:40,286 autoreload 31372 33008 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:40,294 autoreload 24708 37156 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:40,294 autoreload 24708 37156 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:40,341 autoreload 36796 28520 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:40,341 autoreload 36796 28520 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:29:41,038 autoreload 16084 38924 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:41,038 autoreload 16084 38924 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:41,076 autoreload 39116 39784 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:41,076 autoreload 39116 39784 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:41,681 autoreload 8176 8396 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:41,681 autoreload 8176 8396 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:41,958 autoreload 17768 13984 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:41,958 autoreload 17768 13984 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:42,007 autoreload 35128 18896 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:42,007 autoreload 35128 18896 Watching for file changes with StatReloader
INFO 2025-05-23 05:29:41,969 autoreload 38448 17280 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:32,193 autoreload 17768 13984 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,193 autoreload 17768 13984 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,299 autoreload 8176 8396 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,299 autoreload 8176 8396 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,320 autoreload 35128 18896 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,320 autoreload 35128 18896 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,355 autoreload 16084 38924 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,355 autoreload 16084 38924 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,396 autoreload 38448 17280 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:32,396 autoreload 38448 17280 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:33,598 autoreload 25256 36548 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,598 autoreload 25256 36548 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,685 autoreload 37960 8536 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,685 autoreload 37960 8536 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,765 autoreload 4148 33216 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,765 autoreload 4148 33216 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,820 autoreload 37612 35848 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,820 autoreload 37612 35848 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,853 autoreload 22444 19516 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:33,853 autoreload 22444 19516 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:45,709 autoreload 37960 8536 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:45,709 autoreload 37960 8536 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:45,735 autoreload 4148 33216 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:45,735 autoreload 4148 33216 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:45,882 autoreload 37612 35848 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:45,882 autoreload 37612 35848 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:45,884 autoreload 22444 19516 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:45,884 autoreload 22444 19516 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:46,291 autoreload 25256 36548 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:46,291 autoreload 25256 36548 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\settings.py changed, reloading.
INFO 2025-05-23 05:30:47,141 autoreload 40576 22716 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,141 autoreload 40576 22716 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,216 autoreload 30280 17040 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,216 autoreload 30280 17040 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,228 autoreload 24636 14084 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,228 autoreload 24636 14084 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,330 autoreload 36552 33392 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,330 autoreload 36552 33392 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,341 autoreload 4700 1156 Watching for file changes with StatReloader
INFO 2025-05-23 05:30:47,341 autoreload 4700 1156 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:10,003 autoreload 24636 14084 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:10,003 autoreload 24636 14084 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:10,021 autoreload 30280 17040 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:10,021 autoreload 30280 17040 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:10,061 autoreload 36552 33392 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:10,061 autoreload 36552 33392 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:10,165 autoreload 4700 1156 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:10,165 autoreload 4700 1156 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:11,338 autoreload 7364 7356 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:11,338 autoreload 7364 7356 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:11,345 autoreload 40232 22588 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:11,345 autoreload 40232 22588 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:11,356 autoreload 36468 16000 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:11,356 autoreload 36468 16000 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:11,423 autoreload 4716 30564 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:11,423 autoreload 4716 30564 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:22,046 autoreload 4716 30564 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:22,046 autoreload 4716 30564 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:22,206 autoreload 40232 22588 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:22,206 autoreload 40232 22588 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:22,214 autoreload 7364 7356 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:22,214 autoreload 7364 7356 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:22,222 autoreload 36468 16000 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:22,222 autoreload 36468 16000 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:31:23,364 autoreload 2304 31904 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:23,364 autoreload 2304 31904 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:23,448 autoreload 23716 19768 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:23,448 autoreload 23716 19768 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:23,487 autoreload 35004 33292 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:23,487 autoreload 35004 33292 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:23,503 autoreload 16448 37448 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:23,503 autoreload 16448 37448 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:53,602 autoreload 16448 37448 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:31:53,602 autoreload 16448 37448 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:31:54,160 autoreload 2304 31904 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:31:54,160 autoreload 2304 31904 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:31:54,721 autoreload 20772 18892 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:54,721 autoreload 20772 18892 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:54,826 autoreload 35004 33292 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:31:54,826 autoreload 35004 33292 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:31:54,884 autoreload 23716 19768 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:31:54,884 autoreload 23716 19768 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:31:55,212 autoreload 34696 29156 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:55,212 autoreload 34696 29156 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:56,088 autoreload 30072 12960 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:56,088 autoreload 30072 12960 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:56,103 autoreload 21980 37948 Watching for file changes with StatReloader
INFO 2025-05-23 05:31:56,103 autoreload 21980 37948 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:07,376 autoreload 21980 37948 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:07,376 autoreload 21980 37948 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:07,403 autoreload 30072 12960 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:07,403 autoreload 30072 12960 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:07,459 autoreload 20772 18892 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:07,459 autoreload 20772 18892 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:07,631 autoreload 34696 29156 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:07,631 autoreload 34696 29156 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:08,726 autoreload 40264 38024 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:08,726 autoreload 40264 38024 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:08,748 autoreload 38952 28264 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:08,748 autoreload 38952 28264 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:08,820 autoreload 36488 38572 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:08,820 autoreload 36488 38572 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:08,930 autoreload 39836 7988 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:08,930 autoreload 39836 7988 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:17,849 autoreload 17404 35500 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:17,849 autoreload 17404 35500 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:19,477 autoreload 38952 28264 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:19,477 autoreload 38952 28264 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:19,574 autoreload 40264 38024 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:19,574 autoreload 40264 38024 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:19,587 autoreload 39836 7988 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:19,587 autoreload 39836 7988 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:19,632 autoreload 36488 38572 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:19,632 autoreload 36488 38572 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:32:20,800 autoreload 23000 27792 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:20,800 autoreload 23000 27792 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:20,869 autoreload 31548 17300 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:20,869 autoreload 31548 17300 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:20,894 autoreload 18096 25108 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:20,894 autoreload 18096 25108 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:20,917 autoreload 40116 5440 Watching for file changes with StatReloader
INFO 2025-05-23 05:32:20,917 autoreload 40116 5440 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:21,269 autoreload 40116 5440 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,269 autoreload 40116 5440 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,304 autoreload 31548 17300 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,304 autoreload 31548 17300 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,371 autoreload 18096 25108 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,371 autoreload 18096 25108 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,407 autoreload 23000 27792 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,407 autoreload 23000 27792 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,741 autoreload 17404 35500 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:21,741 autoreload 17404 35500 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:33:22,874 autoreload 30536 40172 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:22,874 autoreload 30536 40172 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:22,882 autoreload 39892 38956 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:22,882 autoreload 39892 38956 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:23,017 autoreload 30476 8020 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:23,017 autoreload 30476 8020 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:23,041 autoreload 436 32172 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:23,041 autoreload 436 32172 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:23,300 autoreload 38524 15976 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:23,300 autoreload 38524 15976 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:38,577 autoreload 30536 40172 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,577 autoreload 30536 40172 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,709 autoreload 39892 38956 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,709 autoreload 39892 38956 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,723 autoreload 436 32172 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,723 autoreload 436 32172 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,810 autoreload 30476 8020 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,810 autoreload 30476 8020 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,847 autoreload 38524 15976 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:38,847 autoreload 38524 15976 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:40,252 autoreload 39184 33024 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,252 autoreload 39184 33024 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,384 autoreload 27032 10192 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,384 autoreload 27032 10192 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,406 autoreload 31228 7820 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,406 autoreload 31228 7820 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,436 autoreload 37424 38060 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,436 autoreload 37424 38060 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,520 autoreload 15760 36132 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:40,520 autoreload 15760 36132 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:51,186 autoreload 39184 33024 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,186 autoreload 39184 33024 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,395 autoreload 27032 10192 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,395 autoreload 27032 10192 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,646 autoreload 15760 36132 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,646 autoreload 15760 36132 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,653 autoreload 31228 7820 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,653 autoreload 31228 7820 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,670 autoreload 37424 38060 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:51,670 autoreload 37424 38060 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-05-23 05:33:52,690 autoreload 25696 36004 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:52,690 autoreload 25696 36004 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:52,900 autoreload 7204 29652 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:52,900 autoreload 7204 29652 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:53,202 autoreload 33204 27476 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:53,202 autoreload 33204 27476 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:53,204 autoreload 36040 35996 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:53,204 autoreload 36040 35996 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:53,210 autoreload 14836 37908 Watching for file changes with StatReloader
INFO 2025-05-23 05:33:53,210 autoreload 14836 37908 Watching for file changes with StatReloader
INFO 2025-05-23 05:34:02,225 autoreload 19628 29808 Watching for file changes with StatReloader
INFO 2025-05-23 05:34:02,225 autoreload 19628 29808 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:01,928 autoreload 14836 37908 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:01,928 autoreload 36040 35996 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:01,928 autoreload 14836 37908 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:01,935 autoreload 19628 29808 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:01,935 autoreload 19628 29808 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:02,005 autoreload 25696 36004 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:02,005 autoreload 25696 36004 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:02,056 autoreload 7204 29652 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:02,056 autoreload 7204 29652 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:03,688 autoreload 29024 17868 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,688 autoreload 29024 17868 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,694 autoreload 5500 7008 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,694 autoreload 5500 7008 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,714 autoreload 33884 31948 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,714 autoreload 33884 31948 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,763 autoreload 5508 25632 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,763 autoreload 5508 25632 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,775 autoreload 36584 31272 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:03,775 autoreload 36584 31272 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:21,022 autoreload 29024 17868 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,022 autoreload 29024 17868 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,041 autoreload 36584 31272 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,041 autoreload 36584 31272 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,087 autoreload 5508 25632 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,087 autoreload 5508 25632 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,226 autoreload 5500 7008 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,226 autoreload 5500 7008 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,259 autoreload 33884 31948 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:21,259 autoreload 33884 31948 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:22,741 autoreload 23848 36160 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,741 autoreload 23848 36160 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,775 autoreload 21592 32776 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,775 autoreload 21592 32776 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,805 autoreload 19668 7932 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,805 autoreload 19668 7932 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,931 autoreload 19624 19780 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,931 autoreload 19624 19780 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,993 autoreload 20160 32652 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:22,993 autoreload 20160 32652 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:35,361 autoreload 21592 32776 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:35,361 autoreload 21592 32776 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:36,588 autoreload 20160 32652 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:36,588 autoreload 20160 32652 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:36,621 autoreload 23848 36160 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:36,621 autoreload 23848 36160 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:36,629 autoreload 32396 32084 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:36,629 autoreload 32396 32084 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:36,760 autoreload 19624 19780 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:36,760 autoreload 19624 19780 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:36,770 autoreload 19668 7932 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:36,770 autoreload 19668 7932 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:38,238 autoreload 568 26344 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:38,238 autoreload 568 26344 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:38,251 autoreload 34860 20980 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:38,251 autoreload 34860 20980 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:38,353 autoreload 40284 16736 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:38,353 autoreload 40284 16736 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:38,407 autoreload 7364 32984 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:38,407 autoreload 7364 32984 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:53,456 autoreload 32396 32084 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,456 autoreload 32396 32084 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,723 autoreload 40284 16736 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,723 autoreload 40284 16736 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,746 autoreload 7364 32984 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,746 autoreload 7364 32984 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,750 autoreload 568 26344 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,750 autoreload 568 26344 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,967 autoreload 34860 20980 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:53,967 autoreload 34860 20980 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:40:55,225 autoreload 18936 27984 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,225 autoreload 18936 27984 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,343 autoreload 22068 10392 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,343 autoreload 22068 10392 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,391 autoreload 34792 35356 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,391 autoreload 34792 35356 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,396 autoreload 23176 39104 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,396 autoreload 23176 39104 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,563 autoreload 12952 26436 Watching for file changes with StatReloader
INFO 2025-05-23 05:40:55,563 autoreload 12952 26436 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:16,518 autoreload 12952 26436 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,518 autoreload 12952 26436 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,593 autoreload 22068 10392 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,593 autoreload 22068 10392 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,683 autoreload 18936 27984 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,683 autoreload 18936 27984 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,697 autoreload 34792 35356 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,697 autoreload 34792 35356 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,827 autoreload 23176 39104 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:16,827 autoreload 23176 39104 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-05-23 05:41:18,268 autoreload 30152 37620 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,268 autoreload 30152 37620 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,307 autoreload 33136 2944 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,307 autoreload 33136 2944 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,370 autoreload 40156 35392 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,370 autoreload 40156 35392 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,398 autoreload 15464 20100 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,398 autoreload 15464 20100 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,469 autoreload 17368 17192 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:18,469 autoreload 17368 17192 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:33,509 autoreload 34600 32284 Watching for file changes with StatReloader
INFO 2025-05-23 05:41:33,509 autoreload 34600 32284 Watching for file changes with StatReloader
INFO 2025-05-23 20:46:23,115 autoreload 15464 20100 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\conf\global_settings.py changed, reloading.
INFO 2025-05-23 20:46:23,115 autoreload 15464 20100 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\conf\global_settings.py changed, reloading.
INFO 2025-05-23 20:46:23,119 autoreload 40156 35392 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\apps\__init__.py changed, reloading.
INFO 2025-05-23 20:46:23,119 autoreload 40156 35392 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\apps\__init__.py changed, reloading.
INFO 2025-05-23 20:46:23,287 autoreload 33136 2944 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\__init__.py changed, reloading.
INFO 2025-05-23 20:46:23,287 autoreload 33136 2944 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\__init__.py changed, reloading.
INFO 2025-05-23 20:46:23,316 autoreload 17368 17192 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\conf\locale\__init__.py changed, reloading.
INFO 2025-05-23 20:46:23,316 autoreload 17368 17192 C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\conf\locale\__init__.py changed, reloading.
INFO 2025-06-26 23:43:49,974 autoreload 5768 21716 Watching for file changes with StatReloader
INFO 2025-06-26 23:43:49,974 autoreload 5768 21716 Watching for file changes with StatReloader
INFO 2025-06-26 23:48:05,607 autoreload 22344 23364 Watching for file changes with StatReloader
INFO 2025-06-26 23:48:05,607 autoreload 22344 23364 Watching for file changes with StatReloader
INFO 2025-06-26 23:48:13,533 basehttp 22344 22608 "GET / HTTP/1.1" 302 0
INFO 2025-06-26 23:48:13,533 basehttp 22344 22608 "GET / HTTP/1.1" 302 0
INFO 2025-06-26 23:48:13,586 basehttp 22344 22608 "GET /accounts/login/?next=/ HTTP/1.1" 200 36989
INFO 2025-06-26 23:48:13,586 basehttp 22344 22608 "GET /accounts/login/?next=/ HTTP/1.1" 200 36989
INFO 2025-06-26 23:48:13,698 basehttp 22344 23920 "GET /static/assets/vendor/simple-datatables/style.css HTTP/1.1" 200 3990
INFO 2025-06-26 23:48:13,698 basehttp 22344 23920 "GET /static/assets/vendor/simple-datatables/style.css HTTP/1.1" 200 3990
INFO 2025-06-26 23:48:13,700 basehttp 22344 23356 "GET /static/assets/vendor/quill/quill.snow.css HTTP/1.1" 200 24743
INFO 2025-06-26 23:48:13,700 basehttp 22344 23356 "GET /static/assets/vendor/quill/quill.snow.css HTTP/1.1" 200 24743
INFO 2025-06-26 23:48:13,703 basehttp 22344 6464 "GET /static/assets/vendor/quill/quill.bubble.css HTTP/1.1" 200 25273
INFO 2025-06-26 23:48:13,703 basehttp 22344 6464 "GET /static/assets/vendor/quill/quill.bubble.css HTTP/1.1" 200 25273
INFO 2025-06-26 23:48:13,705 basehttp 22344 23060 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 14803
INFO 2025-06-26 23:48:13,705 basehttp 22344 23060 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 14803
INFO 2025-06-26 23:48:13,707 basehttp 22344 21780 "GET /static/assets/vendor/remixicon/remixicon.css HTTP/1.1" 200 121354
INFO 2025-06-26 23:48:13,707 basehttp 22344 21780 "GET /static/assets/vendor/remixicon/remixicon.css HTTP/1.1" 200 121354
INFO 2025-06-26 23:48:13,711 basehttp 22344 22608 "GET /static/assets/vendor/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 232855
INFO 2025-06-26 23:48:13,711 basehttp 22344 23920 "GET /static/img/dental.jpg HTTP/1.1" 200 180412
INFO 2025-06-26 23:48:13,711 basehttp 22344 22608 "GET /static/assets/vendor/bootstrap/css/bootstrap.min.css HTTP/1.1" 200 232855
INFO 2025-06-26 23:48:13,711 basehttp 22344 23920 "GET /static/img/dental.jpg HTTP/1.1" 200 180412
INFO 2025-06-26 23:48:13,721 basehttp 22344 23060 "GET /static/js/bootstrap.bundle.min.js HTTP/1.1" 200 78129
INFO 2025-06-26 23:48:13,721 basehttp 22344 23060 "GET /static/js/bootstrap.bundle.min.js HTTP/1.1" 200 78129
INFO 2025-06-26 23:48:13,873 basehttp 22344 23060 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 29444
INFO 2025-06-26 23:48:13,873 basehttp 22344 23060 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 29444
INFO 2025-06-26 23:48:13,987 basehttp 22344 23060 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
INFO 2025-06-26 23:48:13,987 basehttp 22344 23060 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
INFO 2025-06-26 23:48:14,032 basehttp 22344 23060 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4692
INFO 2025-06-26 23:48:14,032 basehttp 22344 23060 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4692
INFO 2025-06-26 23:48:33,668 basehttp 22344 23060 "POST /accounts/login/?next=/ HTTP/1.1" 200 37302
INFO 2025-06-26 23:48:33,668 basehttp 22344 23060 "POST /accounts/login/?next=/ HTTP/1.1" 200 37302
INFO 2025-06-26 23:48:34,005 basehttp 22344 23060 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:48:34,005 basehttp 22344 23060 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:48:34,006 basehttp 22344 23920 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:48:34,005 basehttp 22344 22608 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:48:34,006 basehttp 22344 23920 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:48:34,005 basehttp 22344 22608 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:48:34,029 basehttp 22344 23920 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:48:34,029 basehttp 22344 23920 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:48:57,177 basehttp 22344 6344 "POST /accounts/login/?next=/ HTTP/1.1" 302 0
INFO 2025-06-26 23:48:57,177 basehttp 22344 6344 "POST /accounts/login/?next=/ HTTP/1.1" 302 0
INFO 2025-06-26 23:48:57,197 main 22344 6344 Home view called
INFO 2025-06-26 23:48:57,197 main 22344 6344 Home view called
DEBUG 2025-06-26 23:48:57,198 main 22344 6344 Entering try block in home view
DEBUG 2025-06-26 23:48:57,198 main 22344 6344 Entering try block in home view
DEBUG 2025-06-26 23:48:58,233 main 22344 6344 Creating context dictionary
DEBUG 2025-06-26 23:48:58,233 main 22344 6344 Creating context dictionary
DEBUG 2025-06-26 23:48:58,234 main 22344 6344 Context created with 31 keys
DEBUG 2025-06-26 23:48:58,234 main 22344 6344 Context created with 31 keys
DEBUG 2025-06-26 23:48:58,235 main 22344 6344 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-26 23:48:58,235 main 22344 6344 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-26 23:48:58,235 main 22344 6344 trend_30_days_data length: 31
DEBUG 2025-06-26 23:48:58,235 main 22344 6344 trend_30_days_data length: 31
DEBUG 2025-06-26 23:48:58,236 main 22344 6344 trend_12_months_data length: 13
DEBUG 2025-06-26 23:48:58,236 main 22344 6344 trend_12_months_data length: 13
DEBUG 2025-06-26 23:48:58,237 main 22344 6344 trend_24_months_data length: 25
DEBUG 2025-06-26 23:48:58,237 main 22344 6344 trend_24_months_data length: 25
DEBUG 2025-06-26 23:48:58,237 main 22344 6344 trend_all_time_data length: 25
DEBUG 2025-06-26 23:48:58,237 main 22344 6344 trend_all_time_data length: 25
DEBUG 2025-06-26 23:48:58,239 main 22344 6344 trend_30_days_json is valid JSON
DEBUG 2025-06-26 23:48:58,239 main 22344 6344 trend_30_days_json is valid JSON
INFO 2025-06-26 23:48:58,240 main 22344 6344 Rendering home.html template with context
INFO 2025-06-26 23:48:58,240 main 22344 6344 Rendering home.html template with context
DEBUG 2025-06-26 23:48:58,528 main 22344 6344 Render complete, returning response
DEBUG 2025-06-26 23:48:58,528 main 22344 6344 Render complete, returning response
INFO 2025-06-26 23:48:58,762 basehttp 22344 6344 "GET / HTTP/1.1" 200 218657
INFO 2025-06-26 23:48:58,762 basehttp 22344 6344 "GET / HTTP/1.1" 200 218657
INFO 2025-06-26 23:48:58,795 basehttp 22344 6344 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:48:58,795 basehttp 22344 6344 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:48:58,808 basehttp 22344 6344 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:48:58,808 basehttp 22344 6344 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:48:58,875 basehttp 22344 6344 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:48:58,875 basehttp 22344 6344 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:48:58,996 basehttp 22344 6344 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:48:58,996 basehttp 22344 6344 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:49:48,387 main 22344 6344 Home view called
INFO 2025-06-26 23:49:48,387 main 22344 6344 Home view called
DEBUG 2025-06-26 23:49:48,388 main 22344 6344 Entering try block in home view
DEBUG 2025-06-26 23:49:48,388 main 22344 6344 Entering try block in home view
DEBUG 2025-06-26 23:49:49,517 main 22344 6344 Creating context dictionary
DEBUG 2025-06-26 23:49:49,517 main 22344 6344 Creating context dictionary
DEBUG 2025-06-26 23:49:49,519 main 22344 6344 Context created with 31 keys
DEBUG 2025-06-26 23:49:49,519 main 22344 6344 Context created with 31 keys
DEBUG 2025-06-26 23:49:49,521 main 22344 6344 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-26 23:49:49,521 main 22344 6344 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-26 23:49:49,522 main 22344 6344 trend_30_days_data length: 731
DEBUG 2025-06-26 23:49:49,522 main 22344 6344 trend_30_days_data length: 731
DEBUG 2025-06-26 23:49:49,525 main 22344 6344 trend_12_months_data length: 13
DEBUG 2025-06-26 23:49:49,525 main 22344 6344 trend_12_months_data length: 13
DEBUG 2025-06-26 23:49:49,526 main 22344 6344 trend_24_months_data length: 25
DEBUG 2025-06-26 23:49:49,526 main 22344 6344 trend_24_months_data length: 25
DEBUG 2025-06-26 23:49:49,528 main 22344 6344 trend_all_time_data length: 25
DEBUG 2025-06-26 23:49:49,528 main 22344 6344 trend_all_time_data length: 25
DEBUG 2025-06-26 23:49:49,532 main 22344 6344 trend_30_days_json is valid JSON
DEBUG 2025-06-26 23:49:49,532 main 22344 6344 trend_30_days_json is valid JSON
INFO 2025-06-26 23:49:49,534 main 22344 6344 Rendering home.html template with context
INFO 2025-06-26 23:49:49,534 main 22344 6344 Rendering home.html template with context
DEBUG 2025-06-26 23:49:49,763 main 22344 6344 Render complete, returning response
DEBUG 2025-06-26 23:49:49,763 main 22344 6344 Render complete, returning response
INFO 2025-06-26 23:49:49,956 basehttp 22344 6344 "GET /?range=730 HTTP/1.1" 200 403563
INFO 2025-06-26 23:49:49,956 basehttp 22344 6344 "GET /?range=730 HTTP/1.1" 200 403563
INFO 2025-06-26 23:49:49,999 basehttp 22344 6344 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:49:49,999 basehttp 22344 6344 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:49:50,001 basehttp 22344 3988 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:49:50,001 basehttp 22344 3988 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:49:50,111 basehttp 22344 3988 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:49:50,111 basehttp 22344 3988 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:49:50,119 basehttp 22344 3988 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:49:50,119 basehttp 22344 3988 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:52:29,664 basehttp 22344 3988 "GET /case/list/ HTTP/1.1" 200 171563
INFO 2025-06-26 23:52:29,664 basehttp 22344 3988 "GET /case/list/ HTTP/1.1" 200 171563
INFO 2025-06-26 23:52:29,723 basehttp 22344 3988 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:52:29,723 basehttp 22344 3988 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:52:29,725 basehttp 22344 6344 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:52:29,725 basehttp 22344 6344 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:52:29,894 basehttp 22344 6344 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:52:29,894 basehttp 22344 6344 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:52:29,898 basehttp 22344 6344 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:52:29,898 basehttp 22344 6344 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:52:50,119 basehttp 22344 6344 "GET /items/ HTTP/1.1" 200 127400
INFO 2025-06-26 23:52:50,119 basehttp 22344 6344 "GET /items/ HTTP/1.1" 200 127400
INFO 2025-06-26 23:52:58,029 basehttp 22344 6344 "GET /items/item-inventory/ HTTP/1.1" 200 462350
INFO 2025-06-26 23:52:58,029 basehttp 22344 6344 "GET /items/item-inventory/ HTTP/1.1" 200 462350
INFO 2025-06-26 23:52:58,078 basehttp 22344 3988 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:52:58,078 basehttp 22344 3988 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:52:58,081 basehttp 22344 6344 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:52:58,081 basehttp 22344 6344 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:52:58,098 basehttp 22344 6344 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:52:58,098 basehttp 22344 6344 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:52:58,213 basehttp 22344 6344 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:52:58,213 basehttp 22344 6344 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:53:04,502 main 22344 6344 Home view called
INFO 2025-06-26 23:53:04,502 main 22344 6344 Home view called
DEBUG 2025-06-26 23:53:04,503 main 22344 6344 Entering try block in home view
DEBUG 2025-06-26 23:53:04,503 main 22344 6344 Entering try block in home view
DEBUG 2025-06-26 23:53:05,503 main 22344 6344 Creating context dictionary
DEBUG 2025-06-26 23:53:05,503 main 22344 6344 Creating context dictionary
DEBUG 2025-06-26 23:53:05,504 main 22344 6344 Context created with 31 keys
DEBUG 2025-06-26 23:53:05,504 main 22344 6344 Context created with 31 keys
DEBUG 2025-06-26 23:53:05,505 main 22344 6344 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-26 23:53:05,505 main 22344 6344 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-26 23:53:05,507 main 22344 6344 trend_30_days_data length: 31
DEBUG 2025-06-26 23:53:05,507 main 22344 6344 trend_30_days_data length: 31
DEBUG 2025-06-26 23:53:05,509 main 22344 6344 trend_12_months_data length: 13
DEBUG 2025-06-26 23:53:05,509 main 22344 6344 trend_12_months_data length: 13
DEBUG 2025-06-26 23:53:05,511 main 22344 6344 trend_24_months_data length: 25
DEBUG 2025-06-26 23:53:05,511 main 22344 6344 trend_24_months_data length: 25
DEBUG 2025-06-26 23:53:05,513 main 22344 6344 trend_all_time_data length: 25
DEBUG 2025-06-26 23:53:05,513 main 22344 6344 trend_all_time_data length: 25
DEBUG 2025-06-26 23:53:05,517 main 22344 6344 trend_30_days_json is valid JSON
DEBUG 2025-06-26 23:53:05,517 main 22344 6344 trend_30_days_json is valid JSON
INFO 2025-06-26 23:53:05,519 main 22344 6344 Rendering home.html template with context
INFO 2025-06-26 23:53:05,519 main 22344 6344 Rendering home.html template with context
DEBUG 2025-06-26 23:53:05,651 main 22344 6344 Render complete, returning response
DEBUG 2025-06-26 23:53:05,651 main 22344 6344 Render complete, returning response
INFO 2025-06-26 23:53:05,786 basehttp 22344 6344 "GET / HTTP/1.1" 200 218653
INFO 2025-06-26 23:53:05,786 basehttp 22344 6344 "GET / HTTP/1.1" 200 218653
INFO 2025-06-26 23:53:23,554 basehttp 22344 6344 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83017
INFO 2025-06-26 23:53:23,554 basehttp 22344 6344 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83017
WARNING 2025-06-26 23:53:28,378 log 22344 6344 Forbidden: /accounts/dashboards/staff/
WARNING 2025-06-26 23:53:28,378 log 22344 6344 Forbidden: /accounts/dashboards/staff/
WARNING 2025-06-26 23:53:28,379 basehttp 22344 6344 "GET /accounts/dashboards/staff/ HTTP/1.1" 403 32221
WARNING 2025-06-26 23:53:28,379 basehttp 22344 6344 "GET /accounts/dashboards/staff/ HTTP/1.1" 403 32221
INFO 2025-06-26 23:53:28,431 basehttp 22344 6344 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:53:28,431 basehttp 22344 6344 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-26 23:53:28,432 basehttp 22344 3988 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:53:28,432 basehttp 22344 3988 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-26 23:53:28,433 basehttp 22344 12584 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:53:28,433 basehttp 22344 12584 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-26 23:53:28,470 basehttp 22344 12584 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:53:28,470 basehttp 22344 12584 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-26 23:53:35,859 basehttp 22344 12584 "GET /admin/ HTTP/1.1" 200 57607
INFO 2025-06-26 23:53:35,859 basehttp 22344 12584 "GET /admin/ HTTP/1.1" 200 57607
INFO 2025-06-26 23:53:35,896 basehttp 22344 3988 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
INFO 2025-06-26 23:53:35,896 basehttp 22344 18484 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-06-26 23:53:35,897 basehttp 22344 12584 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
INFO 2025-06-26 23:53:35,896 basehttp 22344 3988 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2808
INFO 2025-06-26 23:53:35,898 basehttp 22344 6344 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
INFO 2025-06-26 23:53:35,896 basehttp 22344 18484 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2810
INFO 2025-06-26 23:53:35,899 basehttp 22344 8560 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-06-26 23:53:35,897 basehttp 22344 12584 "GET /static/admin/css/base.css HTTP/1.1" 200 22120
INFO 2025-06-26 23:53:35,898 basehttp 22344 6344 "GET /static/admin/js/theme.js HTTP/1.1" 200 1653
INFO 2025-06-26 23:53:35,901 basehttp 22344 8784 "GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
INFO 2025-06-26 23:53:35,899 basehttp 22344 8560 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-06-26 23:53:35,901 basehttp 22344 8784 "GET /static/admin/css/responsive.css HTTP/1.1" 200 16565
INFO 2025-06-26 23:53:35,908 basehttp 22344 8784 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-06-26 23:53:35,908 basehttp 22344 8784 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-06-26 23:53:35,915 basehttp 22344 8560 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-06-26 23:53:35,915 basehttp 22344 8784 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-26 23:53:35,915 basehttp 22344 8560 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
INFO 2025-06-26 23:53:35,915 basehttp 22344 8784 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
WARNING 2025-06-26 23:53:36,082 log 22344 8560 Not Found: /favicon.ico
WARNING 2025-06-26 23:53:36,082 log 22344 8560 Not Found: /favicon.ico
WARNING 2025-06-26 23:53:36,083 basehttp 22344 8560 "GET /favicon.ico HTTP/1.1" 404 18315
WARNING 2025-06-26 23:53:36,083 basehttp 22344 8560 "GET /favicon.ico HTTP/1.1" 404 18315
ERROR 2025-06-26 23:53:38,279 log 22344 8560 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-26 23:53:38,279 log 22344 8560 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-26 23:53:38,444 basehttp 22344 8560 "GET /accounts/system-settings/ HTTP/1.1" 500 113382
ERROR 2025-06-26 23:53:38,444 basehttp 22344 8560 "GET /accounts/system-settings/ HTTP/1.1" 500 113382
INFO 2025-06-26 23:53:54,775 basehttp 22344 8560 "GET /billing/invoices/ HTTP/1.1" 200 103858
INFO 2025-06-26 23:53:54,775 basehttp 22344 8560 "GET /billing/invoices/ HTTP/1.1" 200 103858
INFO 2025-06-27 00:23:12,936 main 22344 9780 Home view called
INFO 2025-06-27 00:23:12,936 main 22344 9780 Home view called
DEBUG 2025-06-27 00:23:12,939 main 22344 9780 Entering try block in home view
DEBUG 2025-06-27 00:23:12,939 main 22344 9780 Entering try block in home view
DEBUG 2025-06-27 00:23:13,966 main 22344 9780 Creating context dictionary
DEBUG 2025-06-27 00:23:13,966 main 22344 9780 Creating context dictionary
DEBUG 2025-06-27 00:23:13,967 main 22344 9780 Context created with 31 keys
DEBUG 2025-06-27 00:23:13,967 main 22344 9780 Context created with 31 keys
DEBUG 2025-06-27 00:23:13,968 main 22344 9780 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-27 00:23:13,968 main 22344 9780 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-27 00:23:13,969 main 22344 9780 trend_30_days_data length: 31
DEBUG 2025-06-27 00:23:13,969 main 22344 9780 trend_30_days_data length: 31
DEBUG 2025-06-27 00:23:13,970 main 22344 9780 trend_12_months_data length: 13
DEBUG 2025-06-27 00:23:13,970 main 22344 9780 trend_12_months_data length: 13
DEBUG 2025-06-27 00:23:13,971 main 22344 9780 trend_24_months_data length: 25
DEBUG 2025-06-27 00:23:13,971 main 22344 9780 trend_24_months_data length: 25
DEBUG 2025-06-27 00:23:13,972 main 22344 9780 trend_all_time_data length: 25
DEBUG 2025-06-27 00:23:13,972 main 22344 9780 trend_all_time_data length: 25
DEBUG 2025-06-27 00:23:13,977 main 22344 9780 trend_30_days_json is valid JSON
DEBUG 2025-06-27 00:23:13,977 main 22344 9780 trend_30_days_json is valid JSON
INFO 2025-06-27 00:23:13,978 main 22344 9780 Rendering home.html template with context
INFO 2025-06-27 00:23:13,978 main 22344 9780 Rendering home.html template with context
DEBUG 2025-06-27 00:23:14,089 main 22344 9780 Render complete, returning response
DEBUG 2025-06-27 00:23:14,089 main 22344 9780 Render complete, returning response
INFO 2025-06-27 00:23:14,312 basehttp 22344 9780 "GET / HTTP/1.1" 200 218649
INFO 2025-06-27 00:23:14,312 basehttp 22344 9780 "GET / HTTP/1.1" 200 218649
INFO 2025-06-27 00:23:14,342 basehttp 22344 9780 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 00:23:14,342 basehttp 22344 9780 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 00:23:14,360 basehttp 22344 9780 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 00:23:14,360 basehttp 22344 9780 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 00:23:14,665 basehttp 22344 9780 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 00:23:14,665 basehttp 22344 9780 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 00:23:14,672 basehttp 22344 9780 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 00:23:14,672 basehttp 22344 9780 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 00:29:54,625 main 22344 3032 Home view called
INFO 2025-06-27 00:29:54,625 main 22344 3032 Home view called
DEBUG 2025-06-27 00:29:54,628 main 22344 3032 Entering try block in home view
DEBUG 2025-06-27 00:29:54,628 main 22344 3032 Entering try block in home view
DEBUG 2025-06-27 00:29:55,495 main 22344 3032 Creating context dictionary
DEBUG 2025-06-27 00:29:55,495 main 22344 3032 Creating context dictionary
DEBUG 2025-06-27 00:29:55,497 main 22344 3032 Context created with 31 keys
DEBUG 2025-06-27 00:29:55,497 main 22344 3032 Context created with 31 keys
DEBUG 2025-06-27 00:29:55,499 main 22344 3032 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-27 00:29:55,499 main 22344 3032 Context keys: ['total_cases', 'cases_today', 'cases_this_week', 'cases_this_month', 'month_growth', 'overdue_cases', 'ready_to_ship', 'in_progress', 'status_counts', 'total_invoiced_month', 'total_paid_month', 'unpaid_invoices', 'recent_cases', 'upcoming_deadlines', 'top_dentists', 'departments', 'trend_30_days_data', 'trend_12_months_data', 'trend_24_months_data', 'trend_all_time_data', 'trend_30_days_json', 'trend_12_months_json', 'trend_24_months_json', 'trend_all_time_json', 'status_json', 'status_chart_data', 'priority_chart_data', 'department_chart_data', 'user', 'today', 'selected_range']
DEBUG 2025-06-27 00:29:55,503 main 22344 3032 trend_30_days_data length: 31
DEBUG 2025-06-27 00:29:55,503 main 22344 3032 trend_30_days_data length: 31
DEBUG 2025-06-27 00:29:55,506 main 22344 3032 trend_12_months_data length: 13
DEBUG 2025-06-27 00:29:55,506 main 22344 3032 trend_12_months_data length: 13
DEBUG 2025-06-27 00:29:55,508 main 22344 3032 trend_24_months_data length: 25
DEBUG 2025-06-27 00:29:55,508 main 22344 3032 trend_24_months_data length: 25
DEBUG 2025-06-27 00:29:55,513 main 22344 3032 trend_all_time_data length: 25
DEBUG 2025-06-27 00:29:55,513 main 22344 3032 trend_all_time_data length: 25
DEBUG 2025-06-27 00:29:55,517 main 22344 3032 trend_30_days_json is valid JSON
DEBUG 2025-06-27 00:29:55,517 main 22344 3032 trend_30_days_json is valid JSON
INFO 2025-06-27 00:29:55,519 main 22344 3032 Rendering home.html template with context
INFO 2025-06-27 00:29:55,519 main 22344 3032 Rendering home.html template with context
DEBUG 2025-06-27 00:29:55,727 main 22344 3032 Render complete, returning response
DEBUG 2025-06-27 00:29:55,727 main 22344 3032 Render complete, returning response
INFO 2025-06-27 00:29:55,861 basehttp 22344 3032 "GET / HTTP/1.1" 200 218648
INFO 2025-06-27 00:29:55,861 basehttp 22344 3032 "GET / HTTP/1.1" 200 218648
INFO 2025-06-27 00:29:55,915 basehttp 22344 3032 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 00:29:55,915 basehttp 22344 3032 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 00:29:55,934 basehttp 22344 3032 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 00:29:55,934 basehttp 22344 3032 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 00:29:56,189 basehttp 22344 3032 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 00:29:56,189 basehttp 22344 3032 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 00:29:56,207 basehttp 22344 3032 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 00:29:56,207 basehttp 22344 3032 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 00:35:40,593 autoreload 22344 23364 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 00:35:40,593 autoreload 22344 23364 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 00:35:41,859 autoreload 12580 6096 Watching for file changes with StatReloader
INFO 2025-06-27 00:35:41,859 autoreload 12580 6096 Watching for file changes with StatReloader
INFO 2025-06-27 00:35:57,928 autoreload 12580 6096 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-06-27 00:35:57,928 autoreload 12580 6096 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-06-27 00:35:58,645 autoreload 24472 4292 Watching for file changes with StatReloader
INFO 2025-06-27 00:35:58,645 autoreload 24472 4292 Watching for file changes with StatReloader
INFO 2025-06-27 00:36:19,009 autoreload 24472 4292 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\dashboard_views.py changed, reloading.
INFO 2025-06-27 00:36:19,009 autoreload 24472 4292 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\dashboard_views.py changed, reloading.
INFO 2025-06-27 00:36:19,731 autoreload 21668 4264 Watching for file changes with StatReloader
INFO 2025-06-27 00:36:19,731 autoreload 21668 4264 Watching for file changes with StatReloader
INFO 2025-06-27 00:36:23,668 autoreload 21668 4264 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\dashboard_views.py changed, reloading.
INFO 2025-06-27 00:36:23,668 autoreload 21668 4264 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\dashboard_views.py changed, reloading.
INFO 2025-06-27 00:36:24,380 autoreload 17748 3212 Watching for file changes with StatReloader
INFO 2025-06-27 00:36:24,380 autoreload 17748 3212 Watching for file changes with StatReloader
INFO 2025-06-27 00:36:28,307 autoreload 17748 3212 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\dashboard_views.py changed, reloading.
INFO 2025-06-27 00:36:28,307 autoreload 17748 3212 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\dashboard_views.py changed, reloading.
INFO 2025-06-27 00:36:29,055 autoreload 14496 1952 Watching for file changes with StatReloader
INFO 2025-06-27 00:36:29,055 autoreload 14496 1952 Watching for file changes with StatReloader
INFO 2025-06-27 00:40:20,181 dashboard_service 8088 25024 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:40:20,181 dashboard_service 8088 25024 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:40:20,222 dashboard_service 8088 25024 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:40:20,222 dashboard_service 8088 25024 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:40:50,906 autoreload 14496 1952 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\dashboard_views.py changed, reloading.
INFO 2025-06-27 00:40:50,906 autoreload 14496 1952 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\dashboard_views.py changed, reloading.
INFO 2025-06-27 00:40:51,671 autoreload 3084 14612 Watching for file changes with StatReloader
INFO 2025-06-27 00:40:51,671 autoreload 3084 14612 Watching for file changes with StatReloader
INFO 2025-06-27 00:40:56,006 dashboard_service 21444 12580 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:40:56,006 dashboard_service 21444 12580 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:40:56,047 dashboard_service 21444 12580 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:40:56,047 dashboard_service 21444 12580 Adjusted dashboard reference date to 2024-10-10
WARNING 2025-06-27 00:40:58,423 log 3084 16756 Not Found: /dashboard_improved
WARNING 2025-06-27 00:40:58,423 log 3084 16756 Not Found: /dashboard_improved
WARNING 2025-06-27 00:40:58,424 basehttp 3084 16756 "GET /dashboard_improved HTTP/1.1" 404 18343
WARNING 2025-06-27 00:40:58,424 basehttp 3084 16756 "GET /dashboard_improved HTTP/1.1" 404 18343
INFO 2025-06-27 00:40:58,494 basehttp 3084 16756 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 00:40:58,495 basehttp 3084 17692 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 00:40:58,494 basehttp 3084 16756 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 00:40:58,495 basehttp 3084 17692 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 00:40:58,496 basehttp 3084 25020 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 00:40:58,496 basehttp 3084 25020 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 00:40:58,559 basehttp 3084 25020 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 00:40:58,559 basehttp 3084 25020 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
WARNING 2025-06-27 00:41:04,139 log 3084 25020 Not Found: /dashboard_improved
WARNING 2025-06-27 00:41:04,139 log 3084 25020 Not Found: /dashboard_improved
WARNING 2025-06-27 00:41:04,140 basehttp 3084 25020 "GET /dashboard_improved HTTP/1.1" 404 18343
WARNING 2025-06-27 00:41:04,140 basehttp 3084 25020 "GET /dashboard_improved HTTP/1.1" 404 18343
INFO 2025-06-27 00:41:08,539 dashboard_service 3084 25020 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:41:08,539 dashboard_service 3084 25020 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:41:09,679 dashboard_views 3084 25020 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:41:09,679 dashboard_views 3084 25020 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:41:10,229 basehttp 3084 25020 "GET / HTTP/1.1" 200 97124
INFO 2025-06-27 00:41:10,229 basehttp 3084 25020 "GET / HTTP/1.1" 200 97124
INFO 2025-06-27 00:41:10,284 basehttp 3084 16756 "GET /static/css/dashboard/components.css HTTP/1.1" 200 9723
INFO 2025-06-27 00:41:10,285 basehttp 3084 25020 "GET /static/css/dashboard/variables.css HTTP/1.1" 200 6094
INFO 2025-06-27 00:41:10,284 basehttp 3084 16756 "GET /static/css/dashboard/components.css HTTP/1.1" 200 9723
INFO 2025-06-27 00:41:10,287 basehttp 3084 17692 "GET /static/css/dashboard/layout.css HTTP/1.1" 200 6203
INFO 2025-06-27 00:41:10,285 basehttp 3084 25020 "GET /static/css/dashboard/variables.css HTTP/1.1" 200 6094
INFO 2025-06-27 00:41:10,287 basehttp 3084 17692 "GET /static/css/dashboard/layout.css HTTP/1.1" 200 6203
INFO 2025-06-27 00:41:10,290 basehttp 3084 21092 "GET /static/css/dashboard/notifications.css HTTP/1.1" 200 8491
INFO 2025-06-27 00:41:10,290 basehttp 3084 21092 "GET /static/css/dashboard/notifications.css HTTP/1.1" 200 8491
INFO 2025-06-27 00:41:10,304 basehttp 3084 21092 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 200 17295
INFO 2025-06-27 00:41:10,304 basehttp 3084 21092 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 200 17295
INFO 2025-06-27 00:41:41,096 dashboard_service 3084 21092 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:41:41,096 dashboard_service 3084 21092 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:41:41,783 dashboard_views 3084 21092 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:41:41,783 dashboard_views 3084 21092 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:41:42,282 basehttp 3084 21092 "GET /?range=730 HTTP/1.1" 200 122333
INFO 2025-06-27 00:41:42,282 basehttp 3084 21092 "GET /?range=730 HTTP/1.1" 200 122333
INFO 2025-06-27 00:42:06,343 dashboard_service 3084 21092 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:42:06,343 dashboard_service 3084 21092 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:42:06,860 dashboard_views 3084 21092 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:42:06,860 dashboard_views 3084 21092 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:42:07,077 basehttp 3084 21092 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 00:42:07,077 basehttp 3084 21092 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 00:42:07,121 basehttp 3084 21092 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 00:42:07,121 basehttp 3084 25020 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 00:42:07,121 basehttp 3084 21092 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 00:42:07,121 basehttp 3084 25020 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 00:51:04,103 autoreload 3084 14612 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 00:51:04,103 autoreload 3084 14612 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 00:51:05,177 autoreload 22580 25272 Watching for file changes with StatReloader
INFO 2025-06-27 00:51:05,177 autoreload 22580 25272 Watching for file changes with StatReloader
INFO 2025-06-27 00:51:10,192 autoreload 22580 25272 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 00:51:10,192 autoreload 22580 25272 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 00:51:11,009 autoreload 5732 21412 Watching for file changes with StatReloader
INFO 2025-06-27 00:51:11,009 autoreload 5732 21412 Watching for file changes with StatReloader
INFO 2025-06-27 00:51:23,365 autoreload 5732 21412 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-06-27 00:51:23,365 autoreload 5732 21412 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-06-27 00:51:24,355 autoreload 12312 23148 Watching for file changes with StatReloader
INFO 2025-06-27 00:51:24,355 autoreload 12312 23148 Watching for file changes with StatReloader
INFO 2025-06-27 00:52:10,904 enhanced_dashboard_service 20248 22400 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:10,904 enhanced_dashboard_service 20248 22400 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:11,753 dashboard_service 20248 22400 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:11,753 dashboard_service 20248 22400 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:11,817 enhanced_dashboard_service 20248 22400 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:11,817 enhanced_dashboard_service 20248 22400 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:40,293 enhanced_dashboard_service 5584 4952 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:40,293 enhanced_dashboard_service 5584 4952 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:41,215 dashboard_service 5584 4952 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:41,215 dashboard_service 5584 4952 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:41,293 enhanced_dashboard_service 5584 4952 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:52:41,293 enhanced_dashboard_service 5584 4952 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:55:18,596 dashboard_service 12312 1616 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:55:18,596 dashboard_service 12312 1616 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:55:18,901 dashboard_views 12312 1616 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:55:18,901 dashboard_views 12312 1616 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:55:19,126 basehttp 12312 1616 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 00:55:19,126 basehttp 12312 1616 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 00:55:19,155 basehttp 12312 1616 "GET /static/css/dashboard/variables.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,155 basehttp 12312 1616 "GET /static/css/dashboard/variables.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,160 basehttp 12312 17276 "GET /static/css/dashboard/components.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,160 basehttp 12312 20840 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,161 basehttp 12312 22840 "GET /static/css/dashboard/layout.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,160 basehttp 12312 17276 "GET /static/css/dashboard/components.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,162 basehttp 12312 1616 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,160 basehttp 12312 20840 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,162 basehttp 12312 24704 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,161 basehttp 12312 22840 "GET /static/css/dashboard/layout.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,162 basehttp 12312 1616 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,162 basehttp 12312 24704 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,173 basehttp 12312 1616 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,173 basehttp 12312 1616 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,279 basehttp 12312 1616 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,279 basehttp 12312 1616 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,284 basehttp 12312 1616 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 00:55:19,284 basehttp 12312 1616 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 00:55:36,633 basehttp 12312 1616 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 00:55:36,633 basehttp 12312 1616 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 00:55:57,014 dashboard_service 12312 1616 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:55:57,014 dashboard_service 12312 1616 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:55:57,183 dashboard_views 12312 1616 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:55:57,183 dashboard_views 12312 1616 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:55:57,259 basehttp 12312 1616 "GET /accounts/dashboard/improved/ HTTP/1.1" 200 97159
INFO 2025-06-27 00:55:57,259 basehttp 12312 1616 "GET /accounts/dashboard/improved/ HTTP/1.1" 200 97159
INFO 2025-06-27 00:56:22,932 enhanced_dashboard_service 12312 1616 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:22,932 enhanced_dashboard_service 12312 1616 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:23,780 dashboard_service 12312 1616 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:23,780 dashboard_service 12312 1616 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:23,799 enhanced_dashboard_views 12312 1616 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 00:56:23,799 enhanced_dashboard_views 12312 1616 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 00:56:33,169 basehttp 12312 1616 - Broken pipe from ('127.0.0.1', 5292)
INFO 2025-06-27 00:56:33,169 basehttp 12312 1616 - Broken pipe from ('127.0.0.1', 5292)
INFO 2025-06-27 00:56:33,189 enhanced_dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:33,189 enhanced_dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:33,408 dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:33,408 dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:33,427 enhanced_dashboard_views 12312 24704 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 00:56:33,427 enhanced_dashboard_views 12312 24704 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 00:56:33,513 basehttp 12312 24704 "GET /accounts/dashboard/enhanced/ HTTP/1.1" 200 115806
INFO 2025-06-27 00:56:33,513 basehttp 12312 24704 "GET /accounts/dashboard/enhanced/ HTTP/1.1" 200 115806
INFO 2025-06-27 00:56:33,542 basehttp 12312 24704 "GET /static/js/dashboard/enhanced-dashboard.js HTTP/1.1" 200 24983
INFO 2025-06-27 00:56:33,542 basehttp 12312 24704 "GET /static/js/dashboard/enhanced-dashboard.js HTTP/1.1" 200 24983
INFO 2025-06-27 00:56:33,976 enhanced_dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:33,976 enhanced_dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:34,172 dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:34,172 dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:56:34,191 enhanced_dashboard_views 12312 24704 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 00:56:34,191 enhanced_dashboard_views 12312 24704 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 00:56:34,270 basehttp 12312 24704 "GET /accounts/dashboard/enhanced/ HTTP/1.1" 200 115804
INFO 2025-06-27 00:56:34,270 basehttp 12312 24704 "GET /accounts/dashboard/enhanced/ HTTP/1.1" 200 115804
INFO 2025-06-27 00:57:19,786 enhanced_dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:57:19,786 enhanced_dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:57:19,980 dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:57:19,980 dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:57:19,998 enhanced_dashboard_views 12312 24704 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 00:57:19,998 enhanced_dashboard_views 12312 24704 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 00:57:20,084 basehttp 12312 24704 "GET /accounts/dashboard/financial/ HTTP/1.1" 200 115802
INFO 2025-06-27 00:57:20,084 basehttp 12312 24704 "GET /accounts/dashboard/financial/ HTTP/1.1" 200 115802
INFO 2025-06-27 00:57:20,120 basehttp 12312 24704 "GET /static/js/dashboard/enhanced-dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 00:57:20,120 basehttp 12312 24704 "GET /static/js/dashboard/enhanced-dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 00:57:52,674 dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:57:52,674 dashboard_service 12312 24704 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 00:57:52,841 dashboard_views 12312 24704 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:57:52,841 dashboard_views 12312 24704 Dashboard loaded successfully for user 1
INFO 2025-06-27 00:57:52,932 basehttp 12312 24704 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 00:57:52,932 basehttp 12312 24704 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 00:57:52,962 basehttp 12312 22840 "GET /static/css/dashboard/components.css HTTP/1.1" 304 0
INFO 2025-06-27 00:57:52,962 basehttp 12312 22840 "GET /static/css/dashboard/components.css HTTP/1.1" 304 0
INFO 2025-06-27 00:57:52,962 basehttp 12312 24704 "GET /static/css/dashboard/layout.css HTTP/1.1" 304 0
INFO 2025-06-27 00:57:52,962 basehttp 12312 24704 "GET /static/css/dashboard/layout.css HTTP/1.1" 304 0
INFO 2025-06-27 00:57:52,966 basehttp 12312 20840 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 00:57:52,966 basehttp 12312 17276 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 00:57:52,966 basehttp 12312 17276 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 00:57:52,966 basehttp 12312 20840 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 01:08:49,012 autoreload 12312 23148 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 01:08:49,012 autoreload 12312 23148 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 01:08:50,080 autoreload 18060 1788 Watching for file changes with StatReloader
INFO 2025-06-27 01:08:50,080 autoreload 18060 1788 Watching for file changes with StatReloader
INFO 2025-06-27 01:08:57,989 autoreload 18060 1788 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 01:08:57,989 autoreload 18060 1788 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 01:08:58,912 autoreload 10304 24944 Watching for file changes with StatReloader
INFO 2025-06-27 01:08:58,912 autoreload 10304 24944 Watching for file changes with StatReloader
INFO 2025-06-27 01:09:09,041 autoreload 10304 24944 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-06-27 01:09:09,041 autoreload 10304 24944 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-06-27 01:09:09,960 autoreload 2380 15600 Watching for file changes with StatReloader
INFO 2025-06-27 01:09:09,960 autoreload 2380 15600 Watching for file changes with StatReloader
INFO 2025-06-27 01:10:01,676 professional_dashboard_service 23508 21668 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:10:01,676 professional_dashboard_service 23508 21668 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:10:02,021 professional_dashboard_service 23508 21668 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:10:02,021 professional_dashboard_service 23508 21668 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:10:02,147 professional_dashboard_service 23508 21668 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:10:02,147 professional_dashboard_service 23508 21668 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:10:04,032 professional_dashboard_service 23508 21668 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:10:04,032 professional_dashboard_service 23508 21668 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:32,409 dashboard_service 2380 9196 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:32,409 dashboard_service 2380 9196 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:32,889 dashboard_views 2380 9196 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:12:32,889 dashboard_views 2380 9196 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:12:33,126 basehttp 2380 9196 "GET / HTTP/1.1" 200 97123
INFO 2025-06-27 01:12:33,126 basehttp 2380 9196 "GET / HTTP/1.1" 200 97123
INFO 2025-06-27 01:12:33,145 basehttp 2380 9196 "GET /static/css/dashboard/variables.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,145 basehttp 2380 9196 "GET /static/css/dashboard/variables.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,149 basehttp 2380 24872 "GET /static/css/dashboard/layout.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,149 basehttp 2380 25420 "GET /static/css/dashboard/components.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,149 basehttp 2380 24872 "GET /static/css/dashboard/layout.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,149 basehttp 2380 25420 "GET /static/css/dashboard/components.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,152 basehttp 2380 10716 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,152 basehttp 2380 10716 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,155 basehttp 2380 20316 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,155 basehttp 2380 20316 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,157 basehttp 2380 20316 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,157 basehttp 2380 20316 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,171 basehttp 2380 20316 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,171 basehttp 2380 20316 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,288 basehttp 2380 20316 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,288 basehttp 2380 20316 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,296 basehttp 2380 20316 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 01:12:33,296 basehttp 2380 20316 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 01:12:41,640 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:41,640 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:41,812 dashboard_views 2380 20316 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:12:41,812 dashboard_views 2380 20316 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:12:41,886 basehttp 2380 20316 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 01:12:41,886 basehttp 2380 20316 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 01:12:42,936 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:42,936 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:43,109 dashboard_views 2380 20316 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:12:43,109 dashboard_views 2380 20316 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:12:43,186 basehttp 2380 20316 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 01:12:43,186 basehttp 2380 20316 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 01:12:48,506 enhanced_dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:48,506 enhanced_dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:49,401 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:49,401 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:12:49,420 enhanced_dashboard_views 2380 20316 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 01:12:49,420 enhanced_dashboard_views 2380 20316 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 01:12:58,761 basehttp 2380 20316 "GET /accounts/dashboard/financial/ HTTP/1.1" 200 115804
INFO 2025-06-27 01:12:58,761 basehttp 2380 20316 "GET /accounts/dashboard/financial/ HTTP/1.1" 200 115804
INFO 2025-06-27 01:12:58,793 basehttp 2380 20316 "GET /static/js/dashboard/enhanced-dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 01:12:58,793 basehttp 2380 20316 "GET /static/js/dashboard/enhanced-dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 01:13:21,834 enhanced_dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:13:21,834 enhanced_dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:13:22,028 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:13:22,028 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:13:22,048 enhanced_dashboard_views 2380 20316 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 01:13:22,048 enhanced_dashboard_views 2380 20316 Enhanced dashboard loaded successfully for user 1
INFO 2025-06-27 01:13:22,148 basehttp 2380 20316 "GET /accounts/dashboard/enhanced/ HTTP/1.1" 200 115804
INFO 2025-06-27 01:13:22,148 basehttp 2380 20316 "GET /accounts/dashboard/enhanced/ HTTP/1.1" 200 115804
INFO 2025-06-27 01:13:28,286 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:13:28,286 dashboard_service 2380 20316 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:13:28,498 dashboard_views 2380 20316 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:13:28,498 dashboard_views 2380 20316 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:13:28,601 basehttp 2380 20316 "GET /accounts/dashboard/improved/ HTTP/1.1" 200 97159
INFO 2025-06-27 01:13:28,601 basehttp 2380 20316 "GET /accounts/dashboard/improved/ HTTP/1.1" 200 97159
INFO 2025-06-27 01:25:14,636 dashboard_service 2380 8960 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:25:14,636 dashboard_service 2380 8960 Adjusted dashboard reference date to 2024-10-10
INFO 2025-06-27 01:25:14,929 dashboard_views 2380 8960 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:25:14,929 dashboard_views 2380 8960 Dashboard loaded successfully for user 1
INFO 2025-06-27 01:25:15,032 basehttp 2380 8960 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 01:25:15,032 basehttp 2380 8960 "GET / HTTP/1.1" 200 97122
INFO 2025-06-27 01:25:15,063 basehttp 2380 8960 "GET /static/css/dashboard/variables.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,063 basehttp 2380 8960 "GET /static/css/dashboard/variables.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,065 basehttp 2380 2588 "GET /static/css/dashboard/layout.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,065 basehttp 2380 23616 "GET /static/css/dashboard/components.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,065 basehttp 2380 2588 "GET /static/css/dashboard/layout.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,065 basehttp 2380 10384 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,065 basehttp 2380 23616 "GET /static/css/dashboard/components.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,066 basehttp 2380 20456 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,065 basehttp 2380 10384 "GET /static/css/dashboard/notifications.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,066 basehttp 2380 20456 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,069 basehttp 2380 2588 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,069 basehttp 2380 2588 "GET /static/js/dashboard/dashboard.js HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,091 basehttp 2380 2588 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,091 basehttp 2380 2588 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,185 basehttp 2380 2588 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,185 basehttp 2380 2588 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,192 basehttp 2380 2588 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 01:25:15,192 basehttp 2380 2588 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 01:39:22,059 basehttp 2380 21192 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 01:39:22,059 basehttp 2380 21192 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 01:39:22,097 basehttp 2380 21192 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 01:39:22,097 basehttp 2380 21192 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 01:39:22,105 basehttp 2380 21192 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 01:39:22,105 basehttp 2380 21192 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 01:39:22,240 basehttp 2380 21192 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 01:39:22,240 basehttp 2380 21192 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 01:39:22,248 basehttp 2380 21192 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 01:39:22,248 basehttp 2380 21192 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 01:58:13,890 autoreload 2380 15600 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 01:58:13,890 autoreload 2380 15600 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 01:58:15,284 autoreload 3620 12372 Watching for file changes with StatReloader
INFO 2025-06-27 01:58:15,284 autoreload 3620 12372 Watching for file changes with StatReloader
INFO 2025-06-27 01:58:21,577 autoreload 3620 12372 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 01:58:21,577 autoreload 3620 12372 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 01:58:22,811 autoreload 9840 19996 Watching for file changes with StatReloader
INFO 2025-06-27 01:58:22,811 autoreload 9840 19996 Watching for file changes with StatReloader
INFO 2025-06-27 01:58:34,236 autoreload 9840 19996 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-06-27 01:58:34,236 autoreload 9840 19996 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py changed, reloading.
INFO 2025-06-27 01:58:35,500 autoreload 25784 25776 Watching for file changes with StatReloader
INFO 2025-06-27 01:58:35,500 autoreload 25784 25776 Watching for file changes with StatReloader
ERROR 2025-06-27 01:59:24,387 executive_dashboard_service 2064 24812 Executive dashboard error: Cannot resolve keyword 'outstanding_amount' into field. Choices are: case, case_id, created_at, currency, currency_id, date, dentist, dentist_id, due_date, finance_invoice_payments, finance_payments, id, invoice_items, journal_entries, notes, patient_name, payments, status, total_amount, updated_at
ERROR 2025-06-27 01:59:24,387 executive_dashboard_service 2064 24812 Executive dashboard error: Cannot resolve keyword 'outstanding_amount' into field. Choices are: case, case_id, created_at, currency, currency_id, date, dentist, dentist_id, due_date, finance_invoice_payments, finance_payments, id, invoice_items, journal_entries, notes, patient_name, payments, status, total_amount, updated_at
INFO 2025-06-27 01:59:51,970 autoreload 25784 25776 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 01:59:51,970 autoreload 25784 25776 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 01:59:52,939 autoreload 5880 7032 Watching for file changes with StatReloader
INFO 2025-06-27 01:59:52,939 autoreload 5880 7032 Watching for file changes with StatReloader
INFO 2025-06-27 02:00:00,717 basehttp 5880 21108 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 02:00:00,717 basehttp 5880 21108 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 02:00:00,752 basehttp 5880 21108 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 02:00:00,752 basehttp 5880 21108 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 02:00:00,767 basehttp 5880 21108 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 02:00:00,767 basehttp 5880 21108 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 02:00:00,892 basehttp 5880 21108 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 02:00:00,892 basehttp 5880 21108 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 02:00:00,899 basehttp 5880 21108 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 02:00:00,899 basehttp 5880 21108 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
WARNING 2025-06-27 02:00:51,950 log 5880 21108 Not Found: /accounts/
WARNING 2025-06-27 02:00:51,950 log 5880 21108 Not Found: /accounts/
WARNING 2025-06-27 02:00:51,951 basehttp 5880 21108 "GET /accounts/ HTTP/1.1" 404 35079
WARNING 2025-06-27 02:00:51,951 basehttp 5880 21108 "GET /accounts/ HTTP/1.1" 404 35079
INFO 2025-06-27 02:01:42,283 basehttp 5880 21108 "GET /accounts/dashboards/ HTTP/1.1" 302 0
INFO 2025-06-27 02:01:42,283 basehttp 5880 21108 "GET /accounts/dashboards/ HTTP/1.1" 302 0
INFO 2025-06-27 02:01:42,564 basehttp 5880 21108 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 02:01:42,564 basehttp 5880 21108 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
ERROR 2025-06-27 02:01:45,105 executive_dashboard_service 5880 21108 Executive dashboard error: Cannot resolve keyword 'id' into field. Choices are: actual_completion, actual_duration, assigned_technicians, attachments, case_items, case_number, casenote, caseteeth, cost_estimate, created_at, created_by, created_by_id, current_stage, current_stage_id, deadline, delay_reason, delivery_date, dentist, dentist_feedback, dentist_id, dentist_user, dentist_user_id, dentists, estimated_completion, estimated_duration, invoice, items, next_stage, next_stage_id, notes, patient, patient_feedback, patient_id, patients, priority, progress_percentage, quality_checks, received_date_time, responsible_department, responsible_department_id, revision_count, schedule, selected_teeth, ship_date_time, special_requirements, stage_history, stagehistory, status, status_changed_at, status_history, tasks, teeth_color, tryouts, workflow_template, workflow_template_id
ERROR 2025-06-27 02:01:45,105 executive_dashboard_service 5880 21108 Executive dashboard error: Cannot resolve keyword 'id' into field. Choices are: actual_completion, actual_duration, assigned_technicians, attachments, case_items, case_number, casenote, caseteeth, cost_estimate, created_at, created_by, created_by_id, current_stage, current_stage_id, deadline, delay_reason, delivery_date, dentist, dentist_feedback, dentist_id, dentist_user, dentist_user_id, dentists, estimated_completion, estimated_duration, invoice, items, next_stage, next_stage_id, notes, patient, patient_feedback, patient_id, patients, priority, progress_percentage, quality_checks, received_date_time, responsible_department, responsible_department_id, revision_count, schedule, selected_teeth, ship_date_time, special_requirements, stage_history, stagehistory, status, status_changed_at, status_history, tasks, teeth_color, tryouts, workflow_template, workflow_template_id
INFO 2025-06-27 02:01:45,107 executive_dashboard_views 5880 21108 Executive dashboard loaded for user 1
INFO 2025-06-27 02:01:45,107 executive_dashboard_views 5880 21108 Executive dashboard loaded for user 1
ERROR 2025-06-27 02:01:45,111 executive_dashboard_views 5880 21108 Executive dashboard error: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:01:45,111 executive_dashboard_views 5880 21108 Executive dashboard error: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:01:45,246 log 5880 21108 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\executive_dashboard_views.py", line 115, in executive_home_view
    return render(request, 'executive_home.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py", line 415, in home
    return executive_home_view(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\executive_dashboard_views.py", line 142, in executive_home_view
    return render(request, 'executive_home.html', error_context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:01:45,246 log 5880 21108 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\executive_dashboard_views.py", line 115, in executive_home_view
    return render(request, 'executive_home.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py", line 415, in home
    return executive_home_view(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\executive_dashboard_views.py", line 142, in executive_home_view
    return render(request, 'executive_home.html', error_context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:01:45,547 basehttp 5880 21108 "GET / HTTP/1.1" **********
ERROR 2025-06-27 02:01:45,547 basehttp 5880 21108 "GET / HTTP/1.1" **********
ERROR 2025-06-27 02:01:50,105 executive_dashboard_service 26304 26324 Executive dashboard error: Cannot resolve keyword 'id' into field. Choices are: actual_completion, actual_duration, assigned_technicians, attachments, case_items, case_number, casenote, caseteeth, cost_estimate, created_at, created_by, created_by_id, current_stage, current_stage_id, deadline, delay_reason, delivery_date, dentist, dentist_feedback, dentist_id, dentist_user, dentist_user_id, dentists, estimated_completion, estimated_duration, invoice, items, next_stage, next_stage_id, notes, patient, patient_feedback, patient_id, patients, priority, progress_percentage, quality_checks, received_date_time, responsible_department, responsible_department_id, revision_count, schedule, selected_teeth, ship_date_time, special_requirements, stage_history, stagehistory, status, status_changed_at, status_history, tasks, teeth_color, tryouts, workflow_template, workflow_template_id
ERROR 2025-06-27 02:01:50,105 executive_dashboard_service 26304 26324 Executive dashboard error: Cannot resolve keyword 'id' into field. Choices are: actual_completion, actual_duration, assigned_technicians, attachments, case_items, case_number, casenote, caseteeth, cost_estimate, created_at, created_by, created_by_id, current_stage, current_stage_id, deadline, delay_reason, delivery_date, dentist, dentist_feedback, dentist_id, dentist_user, dentist_user_id, dentists, estimated_completion, estimated_duration, invoice, items, next_stage, next_stage_id, notes, patient, patient_feedback, patient_id, patients, priority, progress_percentage, quality_checks, received_date_time, responsible_department, responsible_department_id, revision_count, schedule, selected_teeth, ship_date_time, special_requirements, stage_history, stagehistory, status, status_changed_at, status_history, tasks, teeth_color, tryouts, workflow_template, workflow_template_id
INFO 2025-06-27 02:02:13,754 autoreload 5880 7032 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:02:13,754 autoreload 5880 7032 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:02:14,760 autoreload 26052 25820 Watching for file changes with StatReloader
INFO 2025-06-27 02:02:14,760 autoreload 26052 25820 Watching for file changes with StatReloader
ERROR 2025-06-27 02:02:19,753 executive_dashboard_service 26052 9448 Executive dashboard error: Cannot resolve keyword 'id' into field. Choices are: actual_completion, actual_duration, assigned_technicians, attachments, case_items, case_number, casenote, caseteeth, cost_estimate, created_at, created_by, created_by_id, current_stage, current_stage_id, deadline, delay_reason, delivery_date, dentist, dentist_feedback, dentist_id, dentist_user, dentist_user_id, dentists, estimated_completion, estimated_duration, invoice, items, next_stage, next_stage_id, notes, patient, patient_feedback, patient_id, patients, priority, progress_percentage, quality_checks, received_date_time, responsible_department, responsible_department_id, revision_count, schedule, selected_teeth, ship_date_time, special_requirements, stage_history, stagehistory, status, status_changed_at, status_history, tasks, teeth_color, tryouts, workflow_template, workflow_template_id
ERROR 2025-06-27 02:02:19,753 executive_dashboard_service 26052 9448 Executive dashboard error: Cannot resolve keyword 'id' into field. Choices are: actual_completion, actual_duration, assigned_technicians, attachments, case_items, case_number, casenote, caseteeth, cost_estimate, created_at, created_by, created_by_id, current_stage, current_stage_id, deadline, delay_reason, delivery_date, dentist, dentist_feedback, dentist_id, dentist_user, dentist_user_id, dentists, estimated_completion, estimated_duration, invoice, items, next_stage, next_stage_id, notes, patient, patient_feedback, patient_id, patients, priority, progress_percentage, quality_checks, received_date_time, responsible_department, responsible_department_id, revision_count, schedule, selected_teeth, ship_date_time, special_requirements, stage_history, stagehistory, status, status_changed_at, status_history, tasks, teeth_color, tryouts, workflow_template, workflow_template_id
INFO 2025-06-27 02:02:19,754 executive_dashboard_views 26052 9448 Executive dashboard loaded for user 1
INFO 2025-06-27 02:02:19,754 executive_dashboard_views 26052 9448 Executive dashboard loaded for user 1
ERROR 2025-06-27 02:02:19,757 executive_dashboard_views 26052 9448 Executive dashboard error: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:02:19,757 executive_dashboard_views 26052 9448 Executive dashboard error: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:02:19,810 log 26052 9448 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\executive_dashboard_views.py", line 115, in executive_home_view
    return render(request, 'executive_home.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py", line 415, in home
    return executive_home_view(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\executive_dashboard_views.py", line 142, in executive_home_view
    return render(request, 'executive_home.html', error_context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:02:19,810 log 26052 9448 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\executive_dashboard_views.py", line 115, in executive_home_view
    return render(request, 'executive_home.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\main.py", line 415, in home
    return executive_home_view(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\executive_dashboard_views.py", line 142, in executive_home_view
    return render(request, 'executive_home.html', error_context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:02:19,971 basehttp 26052 9448 "GET / HTTP/1.1" **********
ERROR 2025-06-27 02:02:19,971 basehttp 26052 9448 "GET / HTTP/1.1" **********
INFO 2025-06-27 02:02:26,840 autoreload 26052 25820 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:02:26,840 autoreload 26052 25820 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:02:28,070 autoreload 17544 14264 Watching for file changes with StatReloader
INFO 2025-06-27 02:02:28,070 autoreload 17544 14264 Watching for file changes with StatReloader
INFO 2025-06-27 02:02:38,062 autoreload 17544 14264 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:02:38,062 autoreload 17544 14264 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:02:39,191 autoreload 11868 25464 Watching for file changes with StatReloader
INFO 2025-06-27 02:02:39,191 autoreload 11868 25464 Watching for file changes with StatReloader
INFO 2025-06-27 02:02:51,102 autoreload 11868 25464 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:02:51,102 autoreload 11868 25464 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:02:52,175 autoreload 22312 3276 Watching for file changes with StatReloader
INFO 2025-06-27 02:02:52,175 autoreload 22312 3276 Watching for file changes with StatReloader
ERROR 2025-06-27 02:03:02,400 executive_dashboard_service 21076 23448 Executive dashboard error: Cannot resolve keyword 'payment_date' into field. Choices are: account, account_id, amount, created_at, currency, currency_id, date, dentist, dentist_id, id, invoice, invoice_allocations, invoice_id, invoices_paid, journal_entries, payment_method, reference, updated_at
ERROR 2025-06-27 02:03:02,400 executive_dashboard_service 21076 23448 Executive dashboard error: Cannot resolve keyword 'payment_date' into field. Choices are: account, account_id, amount, created_at, currency, currency_id, date, dentist, dentist_id, id, invoice, invoice_allocations, invoice_id, invoices_paid, journal_entries, payment_method, reference, updated_at
ERROR 2025-06-27 02:03:02,416 executive_dashboard_service 21076 23448 Executive dashboard error: Cannot resolve keyword 'payment_date' into field. Choices are: account, account_id, amount, created_at, currency, currency_id, date, dentist, dentist_id, id, invoice, invoice_allocations, invoice_id, invoices_paid, journal_entries, payment_method, reference, updated_at
ERROR 2025-06-27 02:03:02,416 executive_dashboard_service 21076 23448 Executive dashboard error: Cannot resolve keyword 'payment_date' into field. Choices are: account, account_id, amount, created_at, currency, currency_id, date, dentist, dentist_id, id, invoice, invoice_allocations, invoice_id, invoices_paid, journal_entries, payment_method, reference, updated_at
INFO 2025-06-27 02:03:02,416 executive_dashboard_views 21076 23448 Executive dashboard loaded for user 32
INFO 2025-06-27 02:03:02,416 executive_dashboard_views 21076 23448 Executive dashboard loaded for user 32
ERROR 2025-06-27 02:03:02,770 executive_dashboard_views 21076 23448 Executive dashboard error: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
ERROR 2025-06-27 02:03:02,770 executive_dashboard_views 21076 23448 Executive dashboard error: Invalid block tag on line 637: 'endblock'. Did you forget to register or load this tag?
INFO 2025-06-27 02:03:11,258 autoreload 22312 3276 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:03:11,258 autoreload 22312 3276 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:03:12,160 autoreload 25480 26192 Watching for file changes with StatReloader
INFO 2025-06-27 02:03:12,160 autoreload 25480 26192 Watching for file changes with StatReloader
INFO 2025-06-27 02:03:17,837 autoreload 25480 26192 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:03:17,837 autoreload 25480 26192 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\services\executive_dashboard_service.py changed, reloading.
INFO 2025-06-27 02:03:18,897 autoreload 2252 22132 Watching for file changes with StatReloader
INFO 2025-06-27 02:03:18,897 autoreload 2252 22132 Watching for file changes with StatReloader
INFO 2025-06-27 02:03:28,737 executive_dashboard_views 2252 4976 Executive dashboard loaded for user 1
INFO 2025-06-27 02:03:28,737 executive_dashboard_views 2252 4976 Executive dashboard loaded for user 1
INFO 2025-06-27 02:03:28,948 basehttp 2252 4976 "GET / HTTP/1.1" 200 85114
INFO 2025-06-27 02:03:28,948 basehttp 2252 4976 "GET / HTTP/1.1" 200 85114
INFO 2025-06-27 02:05:40,030 executive_dashboard_views 16016 11456 Executive dashboard loaded for user 32
INFO 2025-06-27 02:05:40,030 executive_dashboard_views 16016 11456 Executive dashboard loaded for user 32
INFO 2025-06-27 02:23:25,848 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:23:25,848 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:23:26,220 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/ HTTP/1.1" 200 74323
INFO 2025-06-27 02:23:26,220 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/ HTTP/1.1" 200 74323
INFO 2025-06-27 02:23:26,264 basehttp 2252 21348 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 02:23:26,264 basehttp 2252 21348 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 02:23:26,273 basehttp 2252 21348 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 02:23:26,273 basehttp 2252 21348 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 02:23:26,408 basehttp 2252 21348 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 02:23:26,408 basehttp 2252 21348 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 02:23:26,414 basehttp 2252 21348 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 02:23:26,414 basehttp 2252 21348 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
ERROR 2025-06-27 02:23:36,168 executive_dashboard_service 2252 21348 Executive dashboard error: 'Invoice' object has no attribute 'invoice_number'
ERROR 2025-06-27 02:23:36,168 executive_dashboard_service 2252 21348 Executive dashboard error: 'Invoice' object has no attribute 'invoice_number'
INFO 2025-06-27 02:23:36,170 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:23:36,170 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:23:36,423 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=365 HTTP/1.1" 200 75005
INFO 2025-06-27 02:23:36,423 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=365 HTTP/1.1" 200 75005
ERROR 2025-06-27 02:23:47,663 executive_dashboard_service 2252 21348 Executive dashboard error: 'Invoice' object has no attribute 'invoice_number'
ERROR 2025-06-27 02:23:47,663 executive_dashboard_service 2252 21348 Executive dashboard error: 'Invoice' object has no attribute 'invoice_number'
INFO 2025-06-27 02:23:47,664 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:23:47,664 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:23:47,908 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=180 HTTP/1.1" 200 75004
INFO 2025-06-27 02:23:47,908 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=180 HTTP/1.1" 200 75004
INFO 2025-06-27 02:23:59,128 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:23:59,128 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:23:59,379 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=720 HTTP/1.1" 200 74333
INFO 2025-06-27 02:23:59,379 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=720 HTTP/1.1" 200 74333
INFO 2025-06-27 02:24:06,643 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:24:06,643 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:24:06,925 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=1400 HTTP/1.1" 200 74334
INFO 2025-06-27 02:24:06,925 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=1400 HTTP/1.1" 200 74334
ERROR 2025-06-27 02:24:12,917 executive_dashboard_service 2252 21348 Executive dashboard error: 'Invoice' object has no attribute 'invoice_number'
ERROR 2025-06-27 02:24:12,917 executive_dashboard_service 2252 21348 Executive dashboard error: 'Invoice' object has no attribute 'invoice_number'
INFO 2025-06-27 02:24:12,918 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:24:12,918 executive_dashboard_views 2252 21348 Executive dashboard loaded for user 1
INFO 2025-06-27 02:24:13,192 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=365 HTTP/1.1" 200 75005
INFO 2025-06-27 02:24:13,192 basehttp 2252 21348 "GET /accounts/dashboard/executive-home/?range=365 HTTP/1.1" 200 75005
INFO 2025-06-27 14:35:10,183 executive_dashboard_views 2252 32492 Executive dashboard loaded for user 1
INFO 2025-06-27 14:35:10,183 executive_dashboard_views 2252 32492 Executive dashboard loaded for user 1
INFO 2025-06-27 14:35:10,457 basehttp 2252 32492 "GET / HTTP/1.1" 200 74275
INFO 2025-06-27 14:35:10,457 basehttp 2252 32492 "GET / HTTP/1.1" 200 74275
INFO 2025-06-27 14:35:10,515 basehttp 2252 32492 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 14:35:10,515 basehttp 2252 32492 "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
INFO 2025-06-27 14:35:10,538 basehttp 2252 32492 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 14:35:10,538 basehttp 2252 32492 "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
INFO 2025-06-27 14:35:10,633 basehttp 2252 32492 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 14:35:10,633 basehttp 2252 32492 "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
INFO 2025-06-27 14:35:10,638 basehttp 2252 19232 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 14:35:10,638 basehttp 2252 19232 "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
INFO 2025-06-27 14:35:17,531 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:35:17,531 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:35:17,791 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:35:17,791 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:35:20,796 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:35:20,796 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:35:21,038 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:35:21,038 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:35:31,839 basehttp 2252 19232 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83017
INFO 2025-06-27 14:35:31,839 basehttp 2252 19232 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83017
WARNING 2025-06-27 14:35:58,794 log 2252 19232 Forbidden: /accounts/dashboards/staff/
WARNING 2025-06-27 14:35:58,794 log 2252 19232 Forbidden: /accounts/dashboards/staff/
WARNING 2025-06-27 14:35:58,796 basehttp 2252 19232 "GET /accounts/dashboards/staff/ HTTP/1.1" 403 32222
WARNING 2025-06-27 14:35:58,796 basehttp 2252 19232 "GET /accounts/dashboards/staff/ HTTP/1.1" 403 32222
INFO 2025-06-27 14:36:02,124 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:36:02,124 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:36:02,404 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:36:02,404 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:36:05,716 basehttp 2252 19232 "GET /accounts/activity-log/ HTTP/1.1" 200 72512
INFO 2025-06-27 14:36:05,716 basehttp 2252 19232 "GET /accounts/activity-log/ HTTP/1.1" 200 72512
INFO 2025-06-27 14:36:09,909 basehttp 2252 19232 "GET /accounts/activity-log/ HTTP/1.1" 200 72512
INFO 2025-06-27 14:36:09,909 basehttp 2252 19232 "GET /accounts/activity-log/ HTTP/1.1" 200 72512
INFO 2025-06-27 14:36:12,505 basehttp 2252 19232 "GET /accounts/profile/settings/ HTTP/1.1" 200 63387
INFO 2025-06-27 14:36:12,505 basehttp 2252 19232 "GET /accounts/profile/settings/ HTTP/1.1" 200 63387
INFO 2025-06-27 14:36:16,641 basehttp 2252 19232 "GET /accounts/profile/ HTTP/1.1" 200 81853
INFO 2025-06-27 14:36:16,641 basehttp 2252 19232 "GET /accounts/profile/ HTTP/1.1" 200 81853
INFO 2025-06-27 14:36:25,211 basehttp 2252 19232 "GET /accounts/activity-log/ HTTP/1.1" 200 72512
INFO 2025-06-27 14:36:25,211 basehttp 2252 19232 "GET /accounts/activity-log/ HTTP/1.1" 200 72512
INFO 2025-06-27 14:36:30,317 basehttp 2252 19232 "GET /case/workflow-stages/ HTTP/1.1" 200 75432
INFO 2025-06-27 14:36:30,317 basehttp 2252 19232 "GET /case/workflow-stages/ HTTP/1.1" 200 75432
INFO 2025-06-27 14:36:35,902 basehttp 2252 19232 "GET /case/workflow-stages/create/ HTTP/1.1" 200 62951
INFO 2025-06-27 14:36:35,902 basehttp 2252 19232 "GET /case/workflow-stages/create/ HTTP/1.1" 200 62951
INFO 2025-06-27 14:36:44,184 basehttp 2252 19232 "GET /case/list/ HTTP/1.1" 200 171563
INFO 2025-06-27 14:36:44,184 basehttp 2252 19232 "GET /case/list/ HTTP/1.1" 200 171563
INFO 2025-06-27 14:36:49,833 basehttp 2252 19232 "GET /case/list/?page=751 HTTP/1.1" 200 159918
INFO 2025-06-27 14:36:49,833 basehttp 2252 19232 "GET /case/list/?page=751 HTTP/1.1" 200 159918
INFO 2025-06-27 14:36:55,077 basehttp 2252 19232 "GET /case/list/?page=750 HTTP/1.1" 200 171930
INFO 2025-06-27 14:36:55,077 basehttp 2252 19232 "GET /case/list/?page=750 HTTP/1.1" 200 171930
INFO 2025-06-27 14:37:33,194 basehttp 2252 19232 "GET /case/list/?page=748 HTTP/1.1" 200 172119
INFO 2025-06-27 14:37:33,194 basehttp 2252 19232 "GET /case/list/?page=748 HTTP/1.1" 200 172119
INFO 2025-06-27 14:37:41,046 basehttp 2252 19232 "GET /case/list/?page=747 HTTP/1.1" 200 172230
INFO 2025-06-27 14:37:41,046 basehttp 2252 19232 "GET /case/list/?page=747 HTTP/1.1" 200 172230
INFO 2025-06-27 14:37:45,380 basehttp 2252 19232 "GET /case/list/?page=746 HTTP/1.1" 200 172259
INFO 2025-06-27 14:37:45,380 basehttp 2252 19232 "GET /case/list/?page=746 HTTP/1.1" 200 172259
INFO 2025-06-27 14:37:49,699 basehttp 2252 19232 "GET /case/list/?page=745 HTTP/1.1" 200 172250
INFO 2025-06-27 14:37:49,699 basehttp 2252 19232 "GET /case/list/?page=745 HTTP/1.1" 200 172250
INFO 2025-06-27 14:37:53,582 basehttp 2252 19232 "GET /case/list/?page=743 HTTP/1.1" 200 172273
INFO 2025-06-27 14:37:53,582 basehttp 2252 19232 "GET /case/list/?page=743 HTTP/1.1" 200 172273
INFO 2025-06-27 14:37:56,812 basehttp 2252 19232 "GET /case/list/?page=741 HTTP/1.1" 200 172271
INFO 2025-06-27 14:37:56,812 basehttp 2252 19232 "GET /case/list/?page=741 HTTP/1.1" 200 172271
INFO 2025-06-27 14:38:24,043 basehttp 2252 19232 "GET /case/list/?search=&status=&priority=&case_number=25223&department=&start_date=&end_date=&page_size=10 HTTP/1.1" 200 96509
INFO 2025-06-27 14:38:24,043 basehttp 2252 19232 "GET /case/list/?search=&status=&priority=&case_number=25223&department=&start_date=&end_date=&page_size=10 HTTP/1.1" 200 96509
INFO 2025-06-27 14:38:30,448 basehttp 2252 19232 "GET /case/case/25223/ HTTP/1.1" 200 215366
INFO 2025-06-27 14:38:30,448 basehttp 2252 19232 "GET /case/case/25223/ HTTP/1.1" 200 215366
INFO 2025-06-27 14:39:15,952 basehttp 2252 19232 "GET /billing/invoices/ HTTP/1.1" 200 103858
INFO 2025-06-27 14:39:15,952 basehttp 2252 19232 "GET /billing/invoices/ HTTP/1.1" 200 103858
INFO 2025-06-27 14:39:21,919 basehttp 2252 19232 "GET /billing/invoices/?search=25218&status=&date_from=&date_to= HTTP/1.1" 200 73395
INFO 2025-06-27 14:39:21,919 basehttp 2252 19232 "GET /billing/invoices/?search=25218&status=&date_from=&date_to= HTTP/1.1" 200 73395
INFO 2025-06-27 14:39:32,403 basehttp 2252 19232 "GET /billing/invoices/25218/ HTTP/1.1" 200 76026
INFO 2025-06-27 14:39:32,403 basehttp 2252 19232 "GET /billing/invoices/25218/ HTTP/1.1" 200 76026
DEBUG 2025-06-27 14:40:12,784 document 2252 19232 pisaDocument options:
  src = <_io.BytesIO object at 0x0000022AD8E277E0>
  dest = <_io.BytesIO object at 0x0000022AD8E26070>
  path = ''
  link_callback = None
  xhtml = False
  context_meta = None
DEBUG 2025-06-27 14:40:12,860 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,861 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,863 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,864 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,865 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,870 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,871 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,873 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,874 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,875 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,876 tables 2252 19232 Col widths: [None, None, None, None, None]
DEBUG 2025-06-27 14:40:12,886 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,888 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,891 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,893 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,896 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,899 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,902 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,904 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,907 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,910 tables 2252 19232 None
DEBUG 2025-06-27 14:40:12,911 tables 2252 19232 Col widths: [None, None]
DEBUG 2025-06-27 14:40:12,915 files 2252 19232 FileObject 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAcIAAAHCAQAAAABUY/ToAAACmklEQVR4nO2aW46DMAxFI3UBLImtsyQWUClDbN84BKhG/Zw5/qCl8fHXlV9pqV/aViAhISEhISEh/xBZwl61rPVd4rG/DoelnS7vw2+X1woJeSXtpZrrgUc08/dvOk1nSMiJbNIyuTUHqa+Uw8Ee7bd2EAKFhPxESl+mPqWsSGiQkL8gt6x4PYM10mUJCflI2seUwVKCS62TNiEhJzJMxe7+IS9IyCuZFv3Tooku4ipvpUFCnkhPVF4Fi5HVFwI55T1mMEhIJ9uZD/8eSFnN6mE7nRQJCXkmJbJmrQd3L3NYYllpHbr6LEjIGzJqn4a5ntVqN++kICGfyJdach/hxquS7MYjOCTkhcy8FU2UFcWirNbkpiWBtAgJOZNVIqu5Soqyd5hCWhpbISEvZF7suw7Ntc79U3+FhLyQWj0qb3XNuX9LY7kfgIS8I6O6aZiLGEW5rOr0qj5ISCdzgRRn8tqKbN1Dm+f6CQkZWhq67JjeYqI7hRyWBJCQJ9LkNUxvw8J775C2AlMfDwnZNTf8nK7V1dfMTq99PCSkWxdZLgT6lBf1UGI8qw8S0jjdwsZSyR16jMhquW2ChLzJYIsS1eo/x/CfaSxrJCTkbU+tq7Xoxn072WyPAlhj0QQJ+UCatHJm26M5jxarfavPfTwkpJqjJapgyQWS+UefdVoNQELe9dTKVsWXSkMT1WvkQw8G+e/JtGEXkOQQo0zqg4Qcq2ApcR8rB13UbvmfkstWABIyNGcfe6wo/VF1S6IOvftBQt6QlqN8nyRyy/u1XfJMHBLymVTr1DOYtBljHSTkR1Jl711U9hTShHfzfxNISCddX2qi7KrEYuSpv17VBwlpZ2FJvkevLIXrEAgSciC/MEhISEhISEjIP0L+AKs6TGXgbrSrAAAAAElFTkSuQmCC', Basepath: 'C:\\GPT4_PROJECTS\\DENTAL_LAB - Copy_CLINE\\__dummy__'
DEBUG 2025-06-27 14:40:12,917 tags 2252 19232 Parsing img tag, src: <xhtml2pdf.files.pisaFileObject object at 0x0000022AD8FAEF90>
DEBUG 2025-06-27 14:40:12,918 tags 2252 19232 Attrs: {'src': <xhtml2pdf.files.pisaFileObject object at 0x0000022AD8FAEF90>, 'width': None, 'height': None, 'align': None, 'id': None}
DEBUG 2025-06-27 14:40:12,931 PngImagePlugin 2252 19232 STREAM b'IHDR' 16 13
DEBUG 2025-06-27 14:40:12,932 PngImagePlugin 2252 19232 STREAM b'IDAT' 41 666
WARNING 2025-06-27 14:40:12,933 util 2252 19232 getSize: Not a float '30%'
DEBUG 2025-06-27 14:40:12,966 PngImagePlugin 2252 19232 STREAM b'IHDR' 16 13
DEBUG 2025-06-27 14:40:12,966 PngImagePlugin 2252 19232 STREAM b'IDAT' 41 666
INFO 2025-06-27 14:40:13,146 basehttp 2252 19232 "GET /billing/invoices/25218/pdf/ HTTP/1.1" 200 15785
INFO 2025-06-27 14:40:13,146 basehttp 2252 19232 "GET /billing/invoices/25218/pdf/ HTTP/1.1" 200 15785
INFO 2025-06-27 14:40:25,135 basehttp 2252 19232 "GET /billing/invoices/25218/update/ HTTP/1.1" 200 138169
INFO 2025-06-27 14:40:25,135 basehttp 2252 19232 "GET /billing/invoices/25218/update/ HTTP/1.1" 200 138169
INFO 2025-06-27 14:40:53,499 signals 2252 19232 Created/updated journal entry 25221 for invoice 25218
INFO 2025-06-27 14:40:53,501 views 2252 19232 Invoice object 25218 updated (main form).
INFO 2025-06-27 14:40:53,512 views 2252 19232 Recalculated final total for Invoice 25218: 1500.00
INFO 2025-06-27 14:40:53,527 signals 2252 19232 Created/updated journal entry 25221 for invoice 25218
INFO 2025-06-27 14:40:53,528 views 2252 19232 Final save for updated Invoice 25218 with fields ['total_amount', 'status'].
INFO 2025-06-27 14:40:53,731 basehttp 2252 19232 "POST /billing/invoices/25218/update/ HTTP/1.1" 302 0
INFO 2025-06-27 14:40:53,731 basehttp 2252 19232 "POST /billing/invoices/25218/update/ HTTP/1.1" 302 0
INFO 2025-06-27 14:40:53,900 basehttp 2252 19232 "GET /billing/invoices/25218/ HTTP/1.1" 200 76526
INFO 2025-06-27 14:40:53,900 basehttp 2252 19232 "GET /billing/invoices/25218/ HTTP/1.1" 200 76526
INFO 2025-06-27 14:41:13,509 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:41:13,509 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:41:13,796 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:41:13,796 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:41:23,344 basehttp 2252 19232 "GET /reports/ HTTP/1.1" 200 71116
INFO 2025-06-27 14:41:23,344 basehttp 2252 19232 "GET /reports/ HTTP/1.1" 200 71116
INFO 2025-06-27 14:41:27,676 basehttp 2252 19232 "GET /reports/reports/case-volume/ HTTP/1.1" 200 73528
INFO 2025-06-27 14:41:27,676 basehttp 2252 19232 "GET /reports/reports/case-volume/ HTTP/1.1" 200 73528
INFO 2025-06-27 14:41:27,705 basehttp 2252 19232 "GET /static/css/reports.css HTTP/1.1" 200 4594
INFO 2025-06-27 14:41:27,705 basehttp 2252 19232 "GET /static/css/reports.css HTTP/1.1" 200 4594
INFO 2025-06-27 14:41:27,716 basehttp 2252 19232 "GET /static/js/reports.js HTTP/1.1" 200 10174
INFO 2025-06-27 14:41:27,716 basehttp 2252 19232 "GET /static/js/reports.js HTTP/1.1" 200 10174
INFO 2025-06-27 14:41:35,915 basehttp 2252 19232 "GET /reports/revenue-report/ HTTP/1.1" 200 68535
INFO 2025-06-27 14:41:35,915 basehttp 2252 19232 "GET /reports/revenue-report/ HTTP/1.1" 200 68535
INFO 2025-06-27 14:41:45,034 basehttp 2252 19232 "GET /reports/reports/case-status/ HTTP/1.1" 200 64414
INFO 2025-06-27 14:41:45,034 basehttp 2252 19232 "GET /reports/reports/case-status/ HTTP/1.1" 200 64414
INFO 2025-06-27 14:41:52,230 basehttp 2252 19232 "GET /reports/reports/financial/ HTTP/1.1" 200 81634
INFO 2025-06-27 14:41:52,230 basehttp 2252 19232 "GET /reports/reports/financial/ HTTP/1.1" 200 81634
INFO 2025-06-27 14:42:03,052 basehttp 2252 19232 "GET /reports/reports/financial/?start_date=2024-01-01&end_date=2025-06-27 HTTP/1.1" 200 84906
INFO 2025-06-27 14:42:03,052 basehttp 2252 19232 "GET /reports/reports/financial/?start_date=2024-01-01&end_date=2025-06-27 HTTP/1.1" 200 84906
INFO 2025-06-27 14:43:01,022 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:43:01,022 executive_dashboard_views 2252 19232 Executive dashboard loaded for user 1
INFO 2025-06-27 14:43:01,251 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:43:01,251 basehttp 2252 19232 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 14:45:07,965 basehttp 2252 19232 "GET /reports/ HTTP/1.1" 200 71116
INFO 2025-06-27 14:45:07,965 basehttp 2252 19232 "GET /reports/ HTTP/1.1" 200 71116
INFO 2025-06-27 14:45:12,435 basehttp 2252 19232 "GET /reports/financial-report/ HTTP/1.1" 200 62728
INFO 2025-06-27 14:45:12,435 basehttp 2252 19232 "GET /reports/financial-report/ HTTP/1.1" 200 62728
INFO 2025-06-27 14:45:18,148 basehttp 2252 19232 "GET /reports/ HTTP/1.1" 200 71115
INFO 2025-06-27 14:45:18,148 basehttp 2252 19232 "GET /reports/ HTTP/1.1" 200 71115
INFO 2025-06-27 14:45:24,250 basehttp 2252 19232 "GET /reports/reports/dentist-financials/ HTTP/1.1" 200 89008
INFO 2025-06-27 14:45:24,250 basehttp 2252 19232 "GET /reports/reports/dentist-financials/ HTTP/1.1" 200 89008
INFO 2025-06-27 14:45:41,420 basehttp 2252 19232 "GET /reports/reports/dentist-financials/?start_date=2024-01-01&end_date=2025-06-27 HTTP/1.1" 200 180721
INFO 2025-06-27 14:45:41,420 basehttp 2252 19232 "GET /reports/reports/dentist-financials/?start_date=2024-01-01&end_date=2025-06-27 HTTP/1.1" 200 180721
INFO 2025-06-27 14:54:06,932 basehttp 2252 25008 "GET /ledger/ HTTP/1.1" 200 84149
INFO 2025-06-27 14:54:06,932 basehttp 2252 25008 "GET /ledger/ HTTP/1.1" 200 84149
INFO 2025-06-27 14:54:19,837 basehttp 2252 25008 "GET /ledger/journal-entries/9504/ HTTP/1.1" 200 70065
INFO 2025-06-27 14:54:19,837 basehttp 2252 25008 "GET /ledger/journal-entries/9504/ HTTP/1.1" 200 70065
ERROR 2025-06-27 14:54:43,919 log 2252 25008 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 14:54:43,919 log 2252 25008 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 14:54:44,042 basehttp 2252 25008 "GET /accounts/system-settings/ HTTP/1.1" 500 113384
ERROR 2025-06-27 14:54:44,042 basehttp 2252 25008 "GET /accounts/system-settings/ HTTP/1.1" 500 113384
ERROR 2025-06-27 15:00:58,925 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:00:58,925 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:00:58,994 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113520
ERROR 2025-06-27 15:00:58,994 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113520
ERROR 2025-06-27 15:00:59,793 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:00:59,793 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:00:59,882 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113521
ERROR 2025-06-27 15:00:59,882 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113521
ERROR 2025-06-27 15:01:00,573 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:01:00,573 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:01:00,645 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113520
ERROR 2025-06-27 15:01:00,645 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113520
ERROR 2025-06-27 15:02:39,965 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:39,965 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:40,028 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113520
ERROR 2025-06-27 15:02:40,028 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113520
ERROR 2025-06-27 15:02:41,161 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:41,161 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:41,222 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113520
ERROR 2025-06-27 15:02:41,222 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113520
ERROR 2025-06-27 15:02:41,920 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:41,920 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:42,002 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113521
ERROR 2025-06-27 15:02:42,002 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113521
ERROR 2025-06-27 15:02:42,733 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:42,733 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:42,881 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113521
ERROR 2025-06-27 15:02:42,881 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113521
ERROR 2025-06-27 15:02:43,495 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:43,495 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:02:43,732 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113521
ERROR 2025-06-27 15:02:43,732 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113521
INFO 2025-06-27 15:03:40,798 basehttp 2252 24808 "GET /ledger/journal-entries/9504/ HTTP/1.1" 200 70065
INFO 2025-06-27 15:03:40,798 basehttp 2252 24808 "GET /ledger/journal-entries/9504/ HTTP/1.1" 200 70065
INFO 2025-06-27 15:03:50,065 basehttp 2252 24808 "GET /ledger/ HTTP/1.1" 200 84149
INFO 2025-06-27 15:03:50,065 basehttp 2252 24808 "GET /ledger/ HTTP/1.1" 200 84149
INFO 2025-06-27 15:03:54,629 basehttp 2252 24808 "GET /accounts/profile/password/ HTTP/1.1" 200 64289
INFO 2025-06-27 15:03:54,629 basehttp 2252 24808 "GET /accounts/profile/password/ HTTP/1.1" 200 64289
INFO 2025-06-27 15:03:57,625 basehttp 2252 24808 "GET /accounts/profile/settings/ HTTP/1.1" 200 63387
INFO 2025-06-27 15:03:57,625 basehttp 2252 24808 "GET /accounts/profile/settings/ HTTP/1.1" 200 63387
INFO 2025-06-27 15:04:07,255 basehttp 2252 24808 "GET /accounts/users/ HTTP/1.1" 200 76947
INFO 2025-06-27 15:04:07,255 basehttp 2252 24808 "GET /accounts/users/ HTTP/1.1" 200 76947
INFO 2025-06-27 15:04:20,942 basehttp 2252 24808 "GET /billing/invoices/25218/ HTTP/1.1" 200 76199
INFO 2025-06-27 15:04:20,942 basehttp 2252 24808 "GET /billing/invoices/25218/ HTTP/1.1" 200 76199
INFO 2025-06-27 15:04:21,816 basehttp 2252 24808 "GET /billing/invoices/25218/update/ HTTP/1.1" 200 138234
INFO 2025-06-27 15:04:21,816 basehttp 2252 24808 "GET /billing/invoices/25218/update/ HTTP/1.1" 200 138234
INFO 2025-06-27 15:04:28,642 basehttp 2252 24808 "GET /accounts/profile/settings/ HTTP/1.1" 200 63387
INFO 2025-06-27 15:04:28,642 basehttp 2252 24808 "GET /accounts/profile/settings/ HTTP/1.1" 200 63387
INFO 2025-06-27 15:04:33,083 basehttp 2252 24808 "GET /accounts/profile/ HTTP/1.1" 200 81853
INFO 2025-06-27 15:04:33,083 basehttp 2252 24808 "GET /accounts/profile/ HTTP/1.1" 200 81853
ERROR 2025-06-27 15:04:50,337 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:04:50,337 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:04:50,467 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113373
ERROR 2025-06-27 15:04:50,467 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113373
INFO 2025-06-27 15:09:08,361 executive_dashboard_views 2252 24808 Executive dashboard loaded for user 1
INFO 2025-06-27 15:09:08,361 executive_dashboard_views 2252 24808 Executive dashboard loaded for user 1
INFO 2025-06-27 15:09:08,495 basehttp 2252 24808 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:09:08,495 basehttp 2252 24808 "GET / HTTP/1.1" 200 74274
ERROR 2025-06-27 15:10:09,104 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:10:09,104 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:10:09,156 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:10:09,156 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:10:10,738 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:10:10,738 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:10:10,793 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:10:10,793 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:11:18,390 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:11:18,390 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:11:18,455 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:11:18,455 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:11:19,278 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:11:19,278 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:11:19,341 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:11:19,341 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:13:30,484 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:13:30,484 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:13:30,556 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:13:30,556 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:13:31,446 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:13:31,446 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:13:31,503 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:13:31,503 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:13:32,592 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:13:32,592 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:13:32,668 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:13:32,668 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:14:49,554 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:14:49,554 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:14:49,625 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113510
ERROR 2025-06-27 15:14:49,625 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113510
ERROR 2025-06-27 15:14:50,599 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:14:50,599 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:14:50,658 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:14:50,658 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:14:51,267 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:14:51,267 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:14:51,328 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:14:51,328 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:16:15,428 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:16:15,428 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:16:15,498 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:16:15,498 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:16:16,279 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:16:16,279 log 2252 24808 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:16:16,363 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113510
ERROR 2025-06-27 15:16:16,363 basehttp 2252 24808 "GET /accounts/system-settings/ HTTP/1.1" 500 113510
ERROR 2025-06-27 15:16:51,820 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:16:51,820 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:16:51,889 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:16:51,889 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:16:53,049 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:16:53,049 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:16:53,114 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113510
ERROR 2025-06-27 15:16:53,114 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113510
ERROR 2025-06-27 15:20:02,175 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:20:02,175 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:20:02,233 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:20:02,233 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:20:02,943 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:20:02,943 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:20:03,008 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:20:03,008 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113509
ERROR 2025-06-27 15:20:03,454 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:20:03,454 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: accounts/admin/system_settings.html
ERROR 2025-06-27 15:20:03,514 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113510
ERROR 2025-06-27 15:20:03,514 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 113510
ERROR 2025-06-27 15:23:25,757 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'home' not found. 'home' is not a valid view function or pattern name.
ERROR 2025-06-27 15:23:25,757 log 2252 29532 Internal Server Error: /accounts/system-settings/
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'home' not found. 'home' is not a valid view function or pattern name.
ERROR 2025-06-27 15:23:25,849 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 183255
ERROR 2025-06-27 15:23:25,849 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 500 183255
INFO 2025-06-27 15:24:32,306 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 200 62783
INFO 2025-06-27 15:24:32,306 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 200 62783
INFO 2025-06-27 15:24:41,392 executive_dashboard_views 2252 29532 Executive dashboard loaded for user 1
INFO 2025-06-27 15:24:41,392 executive_dashboard_views 2252 29532 Executive dashboard loaded for user 1
INFO 2025-06-27 15:24:41,762 basehttp 2252 29532 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:24:41,762 basehttp 2252 29532 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:24:44,853 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 200 62784
INFO 2025-06-27 15:24:44,853 basehttp 2252 29532 "GET /accounts/system-settings/ HTTP/1.1" 200 62784
INFO 2025-06-27 15:25:53,786 basehttp 2252 29532 "GET /home-vue HTTP/1.1" 301 0
INFO 2025-06-27 15:25:53,786 basehttp 2252 29532 "GET /home-vue HTTP/1.1" 301 0
INFO 2025-06-27 15:25:55,439 basehttp 2252 26316 "GET /home-vue/ HTTP/1.1" 200 132924
INFO 2025-06-27 15:25:55,439 basehttp 2252 26316 "GET /home-vue/ HTTP/1.1" 200 132924
INFO 2025-06-27 15:25:55,546 basehttp 2252 26316 "GET /static/js/vue/total-cases.js HTTP/1.1" 200 972
INFO 2025-06-27 15:25:55,546 basehttp 2252 26316 "GET /static/js/vue/total-cases.js HTTP/1.1" 200 972
INFO 2025-06-27 15:25:55,559 basehttp 2252 26316 "GET /static/js/vue/overdue-cases.js HTTP/1.1" 200 989
INFO 2025-06-27 15:25:55,560 basehttp 2252 27580 "GET /static/js/vue/cases-today.js HTTP/1.1" 200 972
INFO 2025-06-27 15:25:55,559 basehttp 2252 26316 "GET /static/js/vue/overdue-cases.js HTTP/1.1" 200 989
INFO 2025-06-27 15:25:55,561 basehttp 2252 4720 "GET /static/js/vue/cases-this-month.js HTTP/1.1" 200 1823
INFO 2025-06-27 15:25:55,562 basehttp 2252 27544 "GET /static/js/vue/cases-this-week.js HTTP/1.1" 200 1799
INFO 2025-06-27 15:25:55,560 basehttp 2252 27580 "GET /static/js/vue/cases-today.js HTTP/1.1" 200 972
INFO 2025-06-27 15:25:55,563 basehttp 2252 20948 "GET /static/js/vue/ready-to-ship.js HTTP/1.1" 200 983
INFO 2025-06-27 15:25:55,561 basehttp 2252 4720 "GET /static/js/vue/cases-this-month.js HTTP/1.1" 200 1823
INFO 2025-06-27 15:25:55,562 basehttp 2252 27544 "GET /static/js/vue/cases-this-week.js HTTP/1.1" 200 1799
INFO 2025-06-27 15:25:55,565 basehttp 2252 14708 "GET /static/js/vue/avg-completion.js HTTP/1.1" 200 1121
INFO 2025-06-27 15:25:55,563 basehttp 2252 20948 "GET /static/js/vue/ready-to-ship.js HTTP/1.1" 200 983
INFO 2025-06-27 15:25:55,565 basehttp 2252 14708 "GET /static/js/vue/avg-completion.js HTTP/1.1" 200 1121
INFO 2025-06-27 15:25:55,570 basehttp 2252 26316 "GET /static/js/vue/on-time-rate.js HTTP/1.1" 200 1119
INFO 2025-06-27 15:25:55,570 basehttp 2252 26316 "GET /static/js/vue/on-time-rate.js HTTP/1.1" 200 1119
INFO 2025-06-27 15:25:55,572 basehttp 2252 27580 "GET /static/js/vue/financial-summary.js HTTP/1.1" 200 3053
INFO 2025-06-27 15:25:55,574 basehttp 2252 27544 "GET /static/js/vue/latest-cases.js HTTP/1.1" 200 3491
INFO 2025-06-27 15:25:55,572 basehttp 2252 27580 "GET /static/js/vue/financial-summary.js HTTP/1.1" 200 3053
INFO 2025-06-27 15:25:55,575 basehttp 2252 4720 "GET /static/js/vue/top-dentists.js HTTP/1.1" 200 2776
INFO 2025-06-27 15:25:55,574 basehttp 2252 27544 "GET /static/js/vue/latest-cases.js HTTP/1.1" 200 3491
INFO 2025-06-27 15:25:55,576 basehttp 2252 20948 "GET /static/js/vue/recent-transactions.js HTTP/1.1" 200 4677
INFO 2025-06-27 15:25:55,576 basehttp 2252 20948 "GET /static/js/vue/recent-transactions.js HTTP/1.1" 200 4677
INFO 2025-06-27 15:25:55,636 basehttp 2252 20948 "GET /static/js/dashboard-charts-original.js HTTP/1.1" 200 14232
INFO 2025-06-27 15:25:55,584 basehttp 2252 27580 "GET /static/js/vue/upcoming-deadlines.js HTTP/1.1" 200 4294
INFO 2025-06-27 15:25:55,586 basehttp 2252 14708 "GET /static/js/vue/department-workload.js HTTP/1.1" 200 14419
INFO 2025-06-27 15:25:55,591 basehttp 2252 27544 "GET /static/js/vue/table-filters.js HTTP/1.1" 200 4465
INFO 2025-06-27 15:25:55,575 basehttp 2252 4720 "GET /static/js/vue/top-dentists.js HTTP/1.1" 200 2776
INFO 2025-06-27 15:25:55,582 basehttp 2252 26316 "GET /static/js/vue/recent-notifications.js HTTP/1.1" 200 4125
INFO 2025-06-27 15:25:55,636 basehttp 2252 20948 "GET /static/js/dashboard-charts-original.js HTTP/1.1" 200 14232
INFO 2025-06-27 15:25:55,584 basehttp 2252 27580 "GET /static/js/vue/upcoming-deadlines.js HTTP/1.1" 200 4294
INFO 2025-06-27 15:25:55,586 basehttp 2252 14708 "GET /static/js/vue/department-workload.js HTTP/1.1" 200 14419
INFO 2025-06-27 15:25:55,591 basehttp 2252 27544 "GET /static/js/vue/table-filters.js HTTP/1.1" 200 4465
INFO 2025-06-27 15:25:55,582 basehttp 2252 26316 "GET /static/js/vue/recent-notifications.js HTTP/1.1" 200 4125
INFO 2025-06-27 15:25:55,657 basehttp 2252 4720 "GET /static/js/vue/trend-chart.js HTTP/1.1" 200 16930
INFO 2025-06-27 15:25:55,657 basehttp 2252 4720 "GET /static/js/vue/trend-chart.js HTTP/1.1" 200 16930
INFO 2025-06-27 15:26:50,135 basehttp 2252 4720 "GET /accounts/system-settings/ HTTP/1.1" 200 62783
INFO 2025-06-27 15:26:50,135 basehttp 2252 4720 "GET /accounts/system-settings/ HTTP/1.1" 200 62783
INFO 2025-06-27 15:26:51,133 basehttp 2252 4720 "GET /accounts/system-settings/ HTTP/1.1" 200 62783
INFO 2025-06-27 15:26:51,133 basehttp 2252 4720 "GET /accounts/system-settings/ HTTP/1.1" 200 62783
INFO 2025-06-27 15:27:03,199 basehttp 2252 4720 "GET /accounts/system-settings/ HTTP/1.1" 200 73738
INFO 2025-06-27 15:27:03,199 basehttp 2252 4720 "GET /accounts/system-settings/ HTTP/1.1" 200 73738
INFO 2025-06-27 15:29:27,525 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:29:27,525 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:29:27,789 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:29:27,789 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:31:22,901 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:31:22,901 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:31:23,023 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:31:23,023 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:31:24,332 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:31:24,332 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:31:24,485 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:31:24,485 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:31:38,668 basehttp 2252 4720 "GET /__debug__/render_panel/?store_id=77221f66ae6046b89838e14dd5595f9d&panel_id=SignalsPanel HTTP/1.1" 200 3457
INFO 2025-06-27 15:31:38,668 basehttp 2252 4720 "GET /__debug__/render_panel/?store_id=77221f66ae6046b89838e14dd5595f9d&panel_id=SignalsPanel HTTP/1.1" 200 3457
INFO 2025-06-27 15:32:10,029 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:10,029 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:10,153 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:32:10,153 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:32:12,719 basehttp 2252 4720 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 15:32:12,719 basehttp 2252 4720 "GET /accounts/dashboards/admin/ HTTP/1.1" 200 83016
INFO 2025-06-27 15:32:16,145 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:16,145 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:16,406 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:32:16,406 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:32:19,190 basehttp 2252 4720 "GET /case/list/ HTTP/1.1" 200 171563
INFO 2025-06-27 15:32:19,190 basehttp 2252 4720 "GET /case/list/ HTTP/1.1" 200 171563
INFO 2025-06-27 15:32:27,137 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:27,137 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:27,440 basehttp 2252 4720 "GET / HTTP/1.1" 200 74275
INFO 2025-06-27 15:32:27,440 basehttp 2252 4720 "GET / HTTP/1.1" 200 74275
INFO 2025-06-27 15:32:29,105 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:29,105 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:29,383 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:32:29,383 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:32:30,434 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:30,434 executive_dashboard_views 2252 4720 Executive dashboard loaded for user 1
INFO 2025-06-27 15:32:30,683 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:32:30,683 basehttp 2252 4720 "GET / HTTP/1.1" 200 74274
INFO 2025-06-27 15:41:40,229 autoreload 2252 22132 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 15:41:40,229 autoreload 2252 22132 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 15:41:43,268 autoreload 27988 31220 Watching for file changes with StatReloader
INFO 2025-06-27 15:41:43,268 autoreload 27988 31220 Watching for file changes with StatReloader
INFO 2025-06-27 15:41:56,541 autoreload 27988 31220 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 15:41:56,541 autoreload 27988 31220 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\urls.py changed, reloading.
INFO 2025-06-27 15:41:57,596 autoreload 26148 8624 Watching for file changes with StatReloader
INFO 2025-06-27 15:41:57,596 autoreload 26148 8624 Watching for file changes with StatReloader
INFO 2025-06-27 15:42:06,705 autoreload 26148 8624 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-06-27 15:42:06,705 autoreload 26148 8624 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-06-27 15:42:07,751 autoreload 25640 4928 Watching for file changes with StatReloader
INFO 2025-06-27 15:42:07,751 autoreload 25640 4928 Watching for file changes with StatReloader
INFO 2025-06-27 15:42:15,685 autoreload 25640 4928 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-06-27 15:42:15,685 autoreload 25640 4928 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\LAB\urls.py changed, reloading.
INFO 2025-06-27 15:42:16,680 autoreload 22680 25164 Watching for file changes with StatReloader
INFO 2025-06-27 15:42:16,680 autoreload 22680 25164 Watching for file changes with StatReloader
INFO 2025-06-27 15:42:41,621 autoreload 22680 25164 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:42:41,621 autoreload 22680 25164 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:42:42,784 autoreload 19696 31256 Watching for file changes with StatReloader
INFO 2025-06-27 15:42:42,784 autoreload 19696 31256 Watching for file changes with StatReloader
INFO 2025-06-27 15:42:56,500 autoreload 19696 31256 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:42:56,500 autoreload 19696 31256 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:42:57,625 autoreload 28572 12532 Watching for file changes with StatReloader
INFO 2025-06-27 15:42:57,625 autoreload 28572 12532 Watching for file changes with StatReloader
ERROR 2025-06-27 15:47:17,623 ultimate_dashboard_service 28572 30328 Error calculating operational metrics: QuerySet.annotate() received non-expression(s): 0.
ERROR 2025-06-27 15:47:17,623 ultimate_dashboard_service 28572 30328 Error calculating operational metrics: QuerySet.annotate() received non-expression(s): 0.
ERROR 2025-06-27 15:47:17,626 ultimate_dashboard_service 28572 30328 Error calculating performance analytics: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,626 ultimate_dashboard_service 28572 30328 Error calculating performance analytics: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,628 ultimate_dashboard_service 28572 30328 Error calculating trend analysis: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,628 ultimate_dashboard_service 28572 30328 Error calculating trend analysis: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,652 ultimate_dashboard_service 28572 30328 Error calculating predictive insights: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,652 ultimate_dashboard_service 28572 30328 Error calculating predictive insights: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,680 ultimate_dashboard_service 28572 30328 Error calculating quality metrics: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,680 ultimate_dashboard_service 28572 30328 Error calculating quality metrics: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,686 ultimate_dashboard_service 28572 30328 Error getting real-time alerts: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,686 ultimate_dashboard_service 28572 30328 Error getting real-time alerts: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,688 ultimate_dashboard_service 28572 30328 Error getting current workload: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,688 ultimate_dashboard_service 28572 30328 Error getting current workload: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,690 ultimate_dashboard_service 28572 30328 Error getting chart data: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,690 ultimate_dashboard_service 28572 30328 Error getting chart data: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:47:17,711 ultimate_dashboard_views 28572 30328 Error in ultimate home view: Invalid filter: 'replace'
ERROR 2025-06-27 15:47:17,711 ultimate_dashboard_views 28572 30328 Error in ultimate home view: Invalid filter: 'replace'
ERROR 2025-06-27 15:47:17,790 log 28572 30328 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 154, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'replace'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 167, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'replace'
ERROR 2025-06-27 15:47:17,790 log 28572 30328 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 154, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'replace'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 167, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 862, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'replace'
ERROR 2025-06-27 15:47:18,065 basehttp 28572 30328 "GET / HTTP/1.1" **********
ERROR 2025-06-27 15:47:18,065 basehttp 28572 30328 "GET / HTTP/1.1" **********
INFO 2025-06-27 15:48:25,011 autoreload 28572 12532 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:48:25,011 autoreload 28572 12532 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:48:26,450 autoreload 28452 16536 Watching for file changes with StatReloader
INFO 2025-06-27 15:48:26,450 autoreload 28452 16536 Watching for file changes with StatReloader
INFO 2025-06-27 15:48:34,886 autoreload 28452 16536 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:48:34,886 autoreload 28452 16536 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:48:36,170 autoreload 31580 29328 Watching for file changes with StatReloader
INFO 2025-06-27 15:48:36,170 autoreload 31580 29328 Watching for file changes with StatReloader
ERROR 2025-06-27 15:52:23,074 ultimate_dashboard_service 31580 26724 Error calculating operational metrics: QuerySet.annotate() received non-expression(s): 0.
ERROR 2025-06-27 15:52:23,074 ultimate_dashboard_service 31580 26724 Error calculating operational metrics: QuerySet.annotate() received non-expression(s): 0.
ERROR 2025-06-27 15:52:23,076 ultimate_dashboard_service 31580 26724 Error calculating performance analytics: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,076 ultimate_dashboard_service 31580 26724 Error calculating performance analytics: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,079 ultimate_dashboard_service 31580 26724 Error calculating trend analysis: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,079 ultimate_dashboard_service 31580 26724 Error calculating trend analysis: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,102 ultimate_dashboard_service 31580 26724 Error calculating predictive insights: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,102 ultimate_dashboard_service 31580 26724 Error calculating predictive insights: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,125 ultimate_dashboard_service 31580 26724 Error calculating quality metrics: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,125 ultimate_dashboard_service 31580 26724 Error calculating quality metrics: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,137 ultimate_dashboard_service 31580 26724 Error getting real-time alerts: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,137 ultimate_dashboard_service 31580 26724 Error getting real-time alerts: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,140 ultimate_dashboard_service 31580 26724 Error getting current workload: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,140 ultimate_dashboard_service 31580 26724 Error getting current workload: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,141 ultimate_dashboard_service 31580 26724 Error getting chart data: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,141 ultimate_dashboard_service 31580 26724 Error getting chart data: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:52:23,161 ultimate_dashboard_views 31580 26724 Error in ultimate home view: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:23,161 ultimate_dashboard_views 31580 26724 Error in ultimate home view: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:23,240 log 31580 26724 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 154, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 167, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:23,240 log 31580 26724 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 154, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 167, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:23,507 basehttp 31580 26724 "GET / HTTP/1.1" **********
ERROR 2025-06-27 15:52:23,507 basehttp 31580 26724 "GET / HTTP/1.1" **********
ERROR 2025-06-27 15:52:42,657 ultimate_dashboard_views 31580 26724 Error in ultimate home view: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:42,657 ultimate_dashboard_views 31580 26724 Error in ultimate home view: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:42,736 log 31580 26724 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 154, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 167, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:42,736 log 31580 26724 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 154, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 167, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:42,908 basehttp 31580 26724 "GET / HTTP/1.1" **********
ERROR 2025-06-27 15:52:42,908 basehttp 31580 26724 "GET / HTTP/1.1" **********
ERROR 2025-06-27 15:52:43,810 ultimate_dashboard_views 31580 26724 Error in ultimate home view: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:43,810 ultimate_dashboard_views 31580 26724 Error in ultimate home view: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:43,902 log 31580 26724 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 154, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 167, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:43,902 log 31580 26724 Internal Server Error: /
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 154, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\contrib\auth\decorators.py", line 59, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\views\ultimate_dashboard_views.py", line 167, in ultimate_home_view
    return render(request, 'ultimate_dashboard.html', context)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\backends\django.py", line 79, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 177, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 299, in do_extends
    nodelist = parser.parse()
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\loader_tags.py", line 234, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 518, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 516, in parse
    compiled_result = compile_func(self, token)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\defaulttags.py", line 962, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 489, in parse
    raise self.error(token, e)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 487, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 609, in compile_filter
    return FilterExpression(token, self)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 705, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\env\Lib\site-packages\django\template\base.py", line 615, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'quality_score_class'
ERROR 2025-06-27 15:52:44,114 basehttp 31580 26724 "GET / HTTP/1.1" **********
ERROR 2025-06-27 15:52:44,114 basehttp 31580 26724 "GET / HTTP/1.1" **********
INFO 2025-06-27 15:53:06,888 basehttp 31580 26724 "GET / HTTP/1.1" 200 96528
INFO 2025-06-27 15:53:06,888 basehttp 31580 26724 "GET / HTTP/1.1" 200 96528
ERROR 2025-06-27 15:53:07,682 ultimate_dashboard_service 31580 26724 Error getting chart data: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
ERROR 2025-06-27 15:53:07,682 ultimate_dashboard_service 31580 26724 Error getting chart data: Cannot resolve keyword 'cases' into field. Choices are: availabilities, capacity, capacity_hours, description, id, is_active, manager, manager_id, name, responsible_cases, schedule, scheduleitem, users, workflow_stages, working_hours_end, working_hours_start
INFO 2025-06-27 15:53:07,829 basehttp 31580 26724 "GET /accounts/api/dashboard/charts/?range=30 HTTP/1.1" 200 115
INFO 2025-06-27 15:53:07,829 basehttp 31580 26724 "GET /accounts/api/dashboard/charts/?range=30 HTTP/1.1" 200 115
INFO 2025-06-27 15:53:21,388 autoreload 31580 29328 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:53:21,388 autoreload 31580 29328 C:\GPT4_PROJECTS\DENTAL_LAB - Copy_CLINE\accounts\templatetags\dashboard_extras.py changed, reloading.
INFO 2025-06-27 15:53:22,774 autoreload 32520 19768 Watching for file changes with StatReloader
INFO 2025-06-27 15:53:22,774 autoreload 32520 19768 Watching for file changes with StatReloader
