"""
Admin Dashboard Service
Provides comprehensive admin dashboard data with real database queries
"""

import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any, Optional
from django.db.models import (
    Count, Sum, Avg, Q, F, Case as DjangoCase, When, Value,
    DecimalField, IntegerField, ExpressionWrapper
)
from django.db.models.functions import <PERSON>runcDate, TruncMonth, Coalesce
from django.utils import timezone
from django.core.cache import cache

# Import models
from case.models import Case, Department, Task
from Dentists.models import Dentist
from billing.models import Invoice
from finance.models import Payment
from accounts.models import CustomUser

logger = logging.getLogger(__name__)


class AdminDashboardService:
    """
    Admin dashboard service providing comprehensive system overview
    with real database data
    """
    
    def __init__(self, date_range_days: int = 30):
        self.date_range_days = date_range_days
        self.today = timezone.now().date()
        self.start_date = self.today - timedelta(days=date_range_days)
        self.start_of_month = self.today.replace(day=1)
        self.start_of_year = self.today.replace(month=1, day=1)
        
        # Previous month for comparisons
        if self.start_of_month.month == 1:
            self.last_month_start = self.start_of_month.replace(year=self.start_of_month.year - 1, month=12)
        else:
            self.last_month_start = self.start_of_month.replace(month=self.start_of_month.month - 1)
        
        self.last_month_end = self.start_of_month - timedelta(days=1)
        
        # Cache key
        self.cache_prefix = f"admin_dashboard_{date_range_days}_{self.today}"
    
    def get_comprehensive_admin_data(self) -> Dict[str, Any]:
        """
        Get all admin dashboard data
        """
        cache_key = f"{self.cache_prefix}_comprehensive"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        try:
            # System statistics
            system_stats = self.get_system_statistics()
            
            # Case analytics
            case_analytics = self.get_case_analytics()
            
            # Financial overview
            financial_overview = self.get_financial_overview()
            
            # Department performance
            department_performance = self.get_department_performance()
            
            # Chart data
            chart_data = self.get_chart_data()
            
            # Recent activities
            recent_activities = self.get_recent_activities()
            
            comprehensive_data = {
                **system_stats,
                **case_analytics,
                **financial_overview,
                'departments': department_performance,
                'chart_data': chart_data,
                **recent_activities,
                'last_updated': timezone.now(),
                'date_range_days': self.date_range_days,
            }
            
            # Cache for 10 minutes
            cache.set(cache_key, comprehensive_data, 600)
            return comprehensive_data
            
        except Exception as e:
            logger.error(f"Error getting admin dashboard data: {e}")
            return self._get_fallback_data()
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """
        Get system-wide user and access statistics
        """
        try:
            total_users = CustomUser.objects.count()
            active_users = CustomUser.objects.filter(is_active=True).count()
            staff_count = CustomUser.objects.filter(user_type=1, is_active=True).count()
            dentist_count = CustomUser.objects.filter(user_type=2, is_active=True).count()
            
            # Recent registrations
            recent_users = CustomUser.objects.filter(
                date_joined__gte=self.start_date
            ).count()
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'staff_count': staff_count,
                'dentist_count': dentist_count,
                'recent_users': recent_users,
                'inactive_users': total_users - active_users,
            }
            
        except Exception as e:
            logger.error(f"Error getting system statistics: {e}")
            return {
                'total_users': 0,
                'active_users': 0,
                'staff_count': 0,
                'dentist_count': 0,
                'recent_users': 0,
                'inactive_users': 0,
            }
    
    def get_case_analytics(self) -> Dict[str, Any]:
        """
        Get comprehensive case analytics
        """
        try:
            # Total cases
            total_cases = Case.objects.count()
            active_cases = Case.objects.filter(
                status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
            ).count()
            
            # Monthly cases
            cases_this_month = Case.objects.filter(
                created_at__gte=self.start_of_month
            ).count()
            
            cases_last_month = Case.objects.filter(
                created_at__gte=self.last_month_start,
                created_at__lte=self.last_month_end
            ).count()
            
            # Case status distribution
            status_distribution = Case.objects.values('status').annotate(
                count=Count('case_number')
            ).order_by('-count')
            
            # Priority distribution
            priority_distribution = Case.objects.values('priority').annotate(
                count=Count('case_number')
            ).order_by('priority')
            
            # Completion metrics
            completed_cases = Case.objects.filter(
                status__in=['completed', 'delivered'],
                actual_completion__isnull=False
            )
            
            # Average completion time
            avg_completion_time = completed_cases.aggregate(
                avg_time=Avg(
                    ExpressionWrapper(
                        F('actual_completion') - F('created_at'),
                        output_field=IntegerField()
                    )
                )
            )['avg_time']
            
            avg_completion_days = (avg_completion_time / (24 * 60 * 60)) if avg_completion_time else 0
            
            # On-time delivery rate
            on_time_cases = completed_cases.filter(
                actual_completion__lte=F('deadline')
            ).count()
            
            on_time_rate = (on_time_cases / completed_cases.count() * 100) if completed_cases.count() > 0 else 0
            
            # Overdue cases
            overdue_cases = Case.objects.filter(
                deadline__lt=timezone.now(),
                status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
            ).count()
            
            return {
                'total_cases': total_cases,
                'active_cases': active_cases,
                'cases_this_month': cases_this_month,
                'cases_last_month': cases_last_month,
                'case_growth': self._calculate_growth_rate(cases_this_month, cases_last_month),
                'status_distribution': list(status_distribution),
                'priority_distribution': list(priority_distribution),
                'avg_completion_days': round(avg_completion_days, 1),
                'on_time_delivery_rate': round(on_time_rate, 1),
                'overdue_cases': overdue_cases,
                'completed_cases_count': completed_cases.count(),
            }
            
        except Exception as e:
            logger.error(f"Error getting case analytics: {e}")
            return {
                'total_cases': 0,
                'active_cases': 0,
                'cases_this_month': 0,
                'cases_last_month': 0,
                'case_growth': 0,
                'status_distribution': [],
                'priority_distribution': [],
                'avg_completion_days': 0,
                'on_time_delivery_rate': 0,
                'overdue_cases': 0,
                'completed_cases_count': 0,
            }
    
    def get_financial_overview(self) -> Dict[str, Any]:
        """
        Get financial overview and invoice statistics
        """
        try:
            # Invoice statistics
            total_invoices = Invoice.objects.count()
            paid_invoices = Invoice.objects.filter(status='paid').count()
            unpaid_invoices = Invoice.objects.filter(
                status__in=['unpaid', 'partial']
            ).count()
            overdue_invoices = Invoice.objects.filter(
                status__in=['unpaid', 'partial'],
                due_date__lt=self.today
            ).count()
            
            # Revenue calculations
            total_revenue = Invoice.objects.aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            monthly_revenue = Invoice.objects.filter(
                date__gte=self.start_of_month
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            last_month_revenue = Invoice.objects.filter(
                date__gte=self.last_month_start,
                date__lte=self.last_month_end
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            # Outstanding amounts
            outstanding_amount = Invoice.objects.filter(
                status__in=['unpaid', 'partial']
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            # Average invoice value
            avg_invoice_value = Invoice.objects.aggregate(
                avg=Coalesce(Avg('total_amount'), Decimal('0.00'))
            )['avg']
            
            return {
                'total_invoices': total_invoices,
                'paid_invoices': paid_invoices,
                'unpaid_invoices': unpaid_invoices,
                'overdue_invoices': overdue_invoices,
                'total_revenue': float(total_revenue),
                'monthly_revenue': float(monthly_revenue),
                'last_month_revenue': float(last_month_revenue),
                'revenue_growth': self._calculate_growth_rate(monthly_revenue, last_month_revenue),
                'outstanding_amount': float(outstanding_amount),
                'avg_invoice_value': float(avg_invoice_value),
                'collection_rate': (paid_invoices / total_invoices * 100) if total_invoices > 0 else 0,
            }
            
        except Exception as e:
            logger.error(f"Error getting financial overview: {e}")
            return {
                'total_invoices': 0,
                'paid_invoices': 0,
                'unpaid_invoices': 0,
                'overdue_invoices': 0,
                'total_revenue': 0.0,
                'monthly_revenue': 0.0,
                'last_month_revenue': 0.0,
                'revenue_growth': 0.0,
                'outstanding_amount': 0.0,
                'avg_invoice_value': 0.0,
                'collection_rate': 0.0,
            }
    
    def _calculate_growth_rate(self, current: float, previous: float) -> float:
        """Calculate percentage growth rate"""
        if previous == 0:
            return 100.0 if current > 0 else 0.0
        return float((current - previous) / previous * 100)
    
    def _get_fallback_data(self) -> Dict[str, Any]:
        """Fallback data on error"""
        return {
            'total_users': 0,
            'active_users': 0,
            'staff_count': 0,
            'dentist_count': 0,
            'total_cases': 0,
            'active_cases': 0,
            'cases_this_month': 0,
            'cases_last_month': 0,
            'departments': [],
            'chart_data': {},
            'recent_cases': [],
            'recent_invoices': [],
            'error': True,
        }

    def get_department_performance(self) -> List[Dict[str, Any]]:
        """
        Get department performance statistics
        """
        try:
            departments = Department.objects.all()
            dept_stats = []

            for dept in departments:
                # Count cases for this department
                case_count = dept.responsible_cases.count()

                # Count active cases
                active_case_count = dept.responsible_cases.filter(
                    status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
                ).count()

                # Count completed cases this month
                completed_this_month = dept.responsible_cases.filter(
                    status__in=['completed', 'delivered'],
                    actual_completion__gte=self.start_of_month
                ).count()

                # Calculate capacity utilization
                capacity_utilization = (active_case_count / dept.capacity * 100) if dept.capacity > 0 else 0

                # Count tasks (if Task model has department relationship)
                try:
                    task_count = Task.objects.filter(
                        case__responsible_department=dept
                    ).count()
                except:
                    task_count = 0

                dept_stats.append({
                    'id': dept.id,
                    'name': dept.name,
                    'case_count': case_count,
                    'active_case_count': active_case_count,
                    'completed_this_month': completed_this_month,
                    'task_count': task_count,
                    'capacity': dept.capacity,
                    'capacity_utilization': round(capacity_utilization, 1),
                    'efficiency_score': self._calculate_department_efficiency(dept),
                })

            return dept_stats

        except Exception as e:
            logger.error(f"Error getting department performance: {e}")
            return []

    def get_chart_data(self) -> Dict[str, Any]:
        """
        Get chart data for visualizations
        """
        try:
            # Case volume trends (last 6 months)
            case_volume_data = []
            for i in range(6, 0, -1):
                month_start = self.today.replace(day=1) - timedelta(days=30 * i)
                month_end = month_start.replace(day=28) + timedelta(days=4)  # End of month
                month_end = month_end - timedelta(days=month_end.day)

                month_cases = Case.objects.filter(
                    created_at__gte=month_start,
                    created_at__lte=month_end
                ).count()

                case_volume_data.append({
                    'month': month_start.strftime('%b %Y'),
                    'cases': month_cases
                })

            # Add current month
            current_month_cases = Case.objects.filter(
                created_at__gte=self.start_of_month
            ).count()

            case_volume_data.append({
                'month': self.start_of_month.strftime('%b %Y'),
                'cases': current_month_cases
            })

            # Department workload data
            departments = Department.objects.all()
            department_data = []

            for dept in departments:
                case_count = dept.responsible_cases.count()
                active_cases = dept.responsible_cases.filter(
                    status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
                ).count()

                department_data.append({
                    'name': dept.name,
                    'total_cases': case_count,
                    'active_cases': active_cases,
                    'capacity': dept.capacity,
                    'utilization': (active_cases / dept.capacity * 100) if dept.capacity > 0 else 0
                })

            # Case status distribution
            status_data = Case.objects.values('status').annotate(
                count=Count('case_number')
            ).order_by('-count')

            # Invoice status distribution
            invoice_data = {
                'paid': Invoice.objects.filter(status='paid').count(),
                'unpaid': Invoice.objects.filter(status='unpaid').count(),
                'partial': Invoice.objects.filter(status='partial').count(),
                'overdue': Invoice.objects.filter(
                    status__in=['unpaid', 'partial'],
                    due_date__lt=self.today
                ).count(),
            }

            return {
                'case_volume_trends': case_volume_data,
                'department_workload': department_data,
                'case_status_distribution': list(status_data),
                'invoice_status_distribution': invoice_data,
            }

        except Exception as e:
            logger.error(f"Error getting chart data: {e}")
            return {
                'case_volume_trends': [],
                'department_workload': [],
                'case_status_distribution': [],
                'invoice_status_distribution': {},
            }

    def get_recent_activities(self) -> Dict[str, Any]:
        """
        Get recent activities and items
        """
        try:
            # Recent cases
            recent_cases = Case.objects.select_related('dentist').order_by('-created_at')[:10]

            # Recent invoices
            recent_invoices = Invoice.objects.select_related('case').order_by('-created_at')[:10]

            # Recent payments
            recent_payments = Payment.objects.order_by('-date')[:5]

            return {
                'recent_cases': recent_cases,
                'recent_invoices': recent_invoices,
                'recent_payments': recent_payments,
            }

        except Exception as e:
            logger.error(f"Error getting recent activities: {e}")
            return {
                'recent_cases': [],
                'recent_invoices': [],
                'recent_payments': [],
            }

    def _calculate_department_efficiency(self, department) -> int:
        """
        Calculate department efficiency score (0-100)
        """
        try:
            # Get completed cases with completion time
            completed_cases = department.responsible_cases.filter(
                status__in=['completed', 'delivered'],
                actual_completion__isnull=False,
                deadline__isnull=False
            )

            if completed_cases.count() == 0:
                return 50  # Neutral score

            # Calculate on-time completion rate
            on_time_cases = completed_cases.filter(
                actual_completion__lte=F('deadline')
            ).count()

            on_time_rate = (on_time_cases / completed_cases.count()) * 100

            # Calculate average completion time vs deadline
            avg_performance = completed_cases.aggregate(
                avg_performance=Avg(
                    ExpressionWrapper(
                        (F('deadline') - F('actual_completion')) / (24 * 60 * 60),
                        output_field=DecimalField(max_digits=10, decimal_places=2)
                    )
                )
            )['avg_performance']

            # Combine metrics for efficiency score
            efficiency_score = min(100, max(0, int(on_time_rate)))

            return efficiency_score

        except Exception as e:
            logger.error(f"Error calculating department efficiency: {e}")
            return 50
