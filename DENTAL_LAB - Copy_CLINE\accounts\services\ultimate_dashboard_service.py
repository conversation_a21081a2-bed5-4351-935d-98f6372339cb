"""
Ultimate Comprehensive Dashboard Service
Provides the most comprehensive dashboard data aggregation with advanced analytics,
financial metrics, operational KPIs, and real-time insights.
"""

import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any, Optional
from django.db.models import (
    Count, Sum, Avg, Q, F, Case as DjangoCase, When, Value,
    DecimalField, IntegerField, ExpressionWrapper
)
from django.db.models.functions import TruncMonth, TruncWeek, Coalesce, Extract
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings

# Import models
from case.models import Case, Department, CaseItem, Task, WorkflowStage
from Dentists.models import Dentist
from billing.models import Invoice
from finance.models import Payment, InvoicePayment
from accounts.models import CustomUser, UserDepartment
from items.models import Item

logger = logging.getLogger(__name__)


class UltimateDashboardService:
    """
    Ultimate comprehensive dashboard service providing:
    - Financial analytics (revenue, payments, outstanding)
    - Operational metrics (efficiency, productivity, quality)
    - Performance analytics (department, user, dentist performance)
    - Predictive insights and trends
    - Real-time KPIs and alerts
    """
    
    def __init__(self, date_range_days: int = 30, user: Optional[CustomUser] = None,
                 start_date: Optional[Any] = None, end_date: Optional[Any] = None):
        self.date_range_days = date_range_days
        self.user = user
        self.today = timezone.now().date()

        # Handle custom date range
        if start_date and end_date:
            self.start_date = start_date
            self.end_date = end_date
        else:
            self.start_date = self.today - timedelta(days=date_range_days)
            self.end_date = self.today

        self.start_of_month = self.today.replace(day=1)
        self.start_of_year = self.today.replace(month=1, day=1)

        # Cache keys
        cache_date_key = f"{self.start_date}_{self.end_date}" if start_date and end_date else str(date_range_days)
        self.cache_prefix = f"ultimate_dashboard_{cache_date_key}_{self.today}"
        
    def get_comprehensive_dashboard_data(self) -> Dict[str, Any]:
        """
        Get all dashboard data in one comprehensive call
        Returns complete dashboard context with all metrics and analytics
        """
        cache_key = f"{self.cache_prefix}_comprehensive"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
            
        try:
            # Core metrics
            financial_overview = self.get_financial_overview()
            operational_metrics = self.get_operational_metrics()
            performance_analytics = self.get_performance_analytics()
            
            # Advanced analytics
            trend_analysis = self.get_trend_analysis()
            predictive_insights = self.get_predictive_insights()
            quality_metrics = self.get_quality_metrics()
            
            # Real-time data
            real_time_alerts = self.get_real_time_alerts()
            current_workload = self.get_current_workload()
            
            # Charts and visualizations
            chart_data = self.get_comprehensive_chart_data()
            
            # Recent activities and notifications
            recent_activities = self.get_recent_activities()
            upcoming_deadlines = self.get_upcoming_deadlines()
            
            comprehensive_data = {
                # Core Metrics
                'financial_overview': financial_overview,
                'operational_metrics': operational_metrics,
                'performance_analytics': performance_analytics,
                
                # Advanced Analytics
                'trend_analysis': trend_analysis,
                'predictive_insights': predictive_insights,
                'quality_metrics': quality_metrics,
                
                # Real-time Data
                'real_time_alerts': real_time_alerts,
                'current_workload': current_workload,
                
                # Visualizations
                'chart_data': chart_data,
                
                # Activities
                'recent_activities': recent_activities,
                'upcoming_deadlines': upcoming_deadlines,
                
                # Meta information
                'date_range_days': self.date_range_days,
                'period_description': self._get_period_description(),
                'last_updated': timezone.now(),
                'user_role': self._get_user_role(),
            }
            
            # Cache for 5 minutes
            cache.set(cache_key, comprehensive_data, 300)
            return comprehensive_data
            
        except Exception as e:
            logger.error(f"Error getting comprehensive dashboard data: {e}")
            return self._get_error_fallback_data()
    
    def get_financial_overview(self) -> Dict[str, Any]:
        """
        Comprehensive financial metrics including revenue, payments, outstanding amounts,
        profit margins, and financial health indicators
        """
        try:
            # Base invoice queryset for the period
            invoices_period = Invoice.objects.filter(
                date__gte=self.start_date,
                date__lte=self.today
            )
            
            # Revenue metrics
            total_invoiced = invoices_period.aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            # Payment metrics
            payments_period = Payment.objects.filter(
                date__gte=self.start_date,
                date__lte=self.today
            )
            
            total_received = payments_period.aggregate(
                total=Coalesce(Sum('amount'), Decimal('0.00'))
            )['total']
            
            # Outstanding amounts
            outstanding_invoices = Invoice.objects.filter(
                status__in=['unpaid', 'partial']
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00')),
                count=Count('id')
            )
            
            # Overdue amounts
            overdue_invoices = Invoice.objects.filter(
                status__in=['unpaid', 'partial'],
                due_date__lt=self.today
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00')),
                count=Count('id')
            )
            
            # Monthly comparison
            last_month_start = (self.start_of_month - timedelta(days=1)).replace(day=1)
            last_month_end = self.start_of_month - timedelta(days=1)
            
            last_month_invoiced = Invoice.objects.filter(
                date__gte=last_month_start,
                date__lte=last_month_end
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            # Calculate growth rates
            current_month_invoiced = Invoice.objects.filter(
                date__gte=self.start_of_month,
                date__lte=self.today
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            revenue_growth = self._calculate_growth_rate(
                current_month_invoiced, last_month_invoiced
            )
            
            # Average invoice value
            avg_invoice_value = invoices_period.aggregate(
                avg=Coalesce(Avg('total_amount'), Decimal('0.00'))
            )['avg']
            
            return {
                'total_invoiced': float(total_invoiced),
                'total_received': float(total_received),
                'outstanding_amount': float(outstanding_invoices['total']),
                'outstanding_count': outstanding_invoices['count'],
                'overdue_amount': float(overdue_invoices['total']),
                'overdue_count': overdue_invoices['count'],
                'revenue_growth': revenue_growth,
                'avg_invoice_value': float(avg_invoice_value),
                'collection_rate': self._calculate_collection_rate(),
                'financial_health_score': self._calculate_financial_health_score(),
            }
            
        except Exception as e:
            logger.error(f"Error calculating financial overview: {e}")
            return self._get_financial_fallback()
    
    def _calculate_growth_rate(self, current: Decimal, previous: Decimal) -> float:
        """Calculate percentage growth rate"""
        if previous == 0:
            return 100.0 if current > 0 else 0.0
        return float((current - previous) / previous * 100)
    
    def _calculate_collection_rate(self) -> float:
        """Calculate the rate at which invoices are being collected"""
        try:
            total_invoiced = Invoice.objects.aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            total_paid = Invoice.objects.filter(
                status='paid'
            ).aggregate(
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )['total']
            
            if total_invoiced == 0:
                return 0.0
            
            return float(total_paid / total_invoiced * 100)
        except Exception:
            return 0.0
    
    def _calculate_financial_health_score(self) -> int:
        """Calculate overall financial health score (0-100)"""
        try:
            collection_rate = self._calculate_collection_rate()
            
            # Get overdue percentage
            total_outstanding = Invoice.objects.filter(
                status__in=['unpaid', 'partial']
            ).aggregate(total=Coalesce(Sum('total_amount'), Decimal('0.00')))['total']
            
            overdue_amount = Invoice.objects.filter(
                status__in=['unpaid', 'partial'],
                due_date__lt=self.today
            ).aggregate(total=Coalesce(Sum('total_amount'), Decimal('0.00')))['total']
            
            overdue_rate = 0.0
            if total_outstanding > 0:
                overdue_rate = float(overdue_amount / total_outstanding * 100)
            
            # Calculate score (weighted average)
            score = (collection_rate * 0.6) + ((100 - overdue_rate) * 0.4)
            return min(100, max(0, int(score)))
            
        except Exception:
            return 50  # Neutral score on error
    
    def _get_financial_fallback(self) -> Dict[str, Any]:
        """Fallback financial data on error"""
        return {
            'total_invoiced': 0.0,
            'total_received': 0.0,
            'outstanding_amount': 0.0,
            'outstanding_count': 0,
            'overdue_amount': 0.0,
            'overdue_count': 0,
            'revenue_growth': 0.0,
            'avg_invoice_value': 0.0,
            'collection_rate': 0.0,
            'financial_health_score': 50,
        }
    
    def _get_period_description(self) -> str:
        """Get human-readable period description"""
        if self.date_range_days == 7:
            return "Last 7 Days"
        elif self.date_range_days == 30:
            return "Last 30 Days"
        elif self.date_range_days == 90:
            return "Last 3 Months"
        elif self.date_range_days == 180:
            return "Last 6 Months"
        elif self.date_range_days == 365:
            return "Last Year"
        else:
            return f"Last {self.date_range_days} Days"
    
    def _get_user_role(self) -> str:
        """Get user role for role-based dashboard customization"""
        if not self.user:
            return 'anonymous'
        
        if self.user.is_superuser:
            return 'admin'
        elif self.user.user_type == 2:  # Dentist
            return 'dentist'
        elif hasattr(self.user, 'departments') and self.user.departments.filter(is_manager=True).exists():
            return 'manager'
        else:
            return 'staff'
    
    def _get_error_fallback_data(self) -> Dict[str, Any]:
        """Fallback data structure on critical error"""
        return {
            'financial_overview': self._get_financial_fallback(),
            'operational_metrics': {},
            'performance_analytics': {},
            'trend_analysis': {},
            'predictive_insights': {},
            'quality_metrics': {},
            'real_time_alerts': [],
            'current_workload': {},
            'chart_data': {},
            'recent_activities': [],
            'upcoming_deadlines': [],
            'date_range_days': self.date_range_days,
            'period_description': self._get_period_description(),
            'last_updated': timezone.now(),
            'user_role': self._get_user_role(),
            'error': True,
        }

    def get_operational_metrics(self) -> Dict[str, Any]:
        """
        Comprehensive operational metrics including case processing efficiency,
        department performance, and productivity indicators
        """
        try:
            # Case volume metrics
            cases_period = Case.objects.filter(
                created_at__date__gte=self.start_date,
                created_at__date__lte=self.today
            )

            total_cases = cases_period.count()

            # Status distribution
            status_counts = cases_period.values('status').annotate(
                count=Count('case_number'),
                percentage=Count('case_number') * 100.0 / total_cases if total_cases > 0 else 0
            ).order_by('-count')

            # Priority distribution
            priority_counts = cases_period.values('priority').annotate(
                count=Count('case_number')
            ).order_by('priority')

            # Completion metrics
            completed_cases = Case.objects.filter(
                status__in=['completed', 'delivered'],
                actual_completion__isnull=False,
                created_at__date__gte=self.start_date
            )

            # Calculate average completion time
            completion_times = completed_cases.annotate(
                completion_time=ExpressionWrapper(
                    F('actual_completion') - F('created_at'),
                    output_field=IntegerField()
                )
            ).aggregate(
                avg_completion=Coalesce(Avg('completion_time'), 0)
            )

            avg_completion_days = completion_times['avg_completion'] / (24 * 60 * 60) if completion_times['avg_completion'] else 0

            # On-time delivery rate
            on_time_cases = completed_cases.filter(
                actual_completion__lte=F('deadline')
            ).count()

            on_time_rate = (on_time_cases / completed_cases.count() * 100) if completed_cases.count() > 0 else 0

            # Overdue cases
            overdue_cases = Case.objects.filter(
                deadline__lt=timezone.now(),
                status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
            ).count()

            # Ready to ship
            ready_to_ship = Case.objects.filter(status='ready_to_ship').count()

            # Department workload
            department_workload = Department.objects.annotate(
                active_cases=Count('responsible_cases', filter=Q(responsible_cases__status__in=['in_progress', 'on_hold'])),
                total_cases=Count('responsible_cases', filter=Q(responsible_cases__created_at__date__gte=self.start_date)),
                capacity_utilization=ExpressionWrapper(
                    Count('responsible_cases', filter=Q(responsible_cases__status__in=['in_progress', 'on_hold'])) * 100.0 / F('capacity'),
                    output_field=DecimalField(max_digits=10, decimal_places=2)
                )
            ).order_by('-capacity_utilization')[:10]

            # Productivity metrics
            cases_today = Case.objects.filter(created_at__date=self.today).count()
            cases_this_week = Case.objects.filter(
                created_at__date__gte=self.today - timedelta(days=self.today.weekday())
            ).count()
            cases_this_month = Case.objects.filter(
                created_at__date__gte=self.start_of_month
            ).count()

            # Quality metrics
            quality_check_cases = Case.objects.filter(
                status='quality_check',
                created_at__date__gte=self.start_date
            ).count()

            rework_cases = Case.objects.filter(
                status='rework_required',
                created_at__date__gte=self.start_date
            ).count()

            quality_rate = ((total_cases - rework_cases) / total_cases * 100) if total_cases > 0 else 100

            return {
                'total_cases': total_cases,
                'cases_today': cases_today,
                'cases_this_week': cases_this_week,
                'cases_this_month': cases_this_month,
                'status_distribution': list(status_counts),
                'priority_distribution': list(priority_counts),
                'avg_completion_days': round(avg_completion_days, 1),
                'on_time_delivery_rate': round(on_time_rate, 1),
                'overdue_cases': overdue_cases,
                'ready_to_ship': ready_to_ship,
                'department_workload': list(department_workload.values(
                    'name', 'active_cases', 'total_cases', 'capacity_utilization'
                )),
                'quality_check_cases': quality_check_cases,
                'rework_cases': rework_cases,
                'quality_rate': round(quality_rate, 1),
                'efficiency_score': self._calculate_efficiency_score(on_time_rate, quality_rate),
            }

        except Exception as e:
            logger.error(f"Error calculating operational metrics: {e}")
            return self._get_operational_fallback()

    def get_performance_analytics(self) -> Dict[str, Any]:
        """
        Performance analytics for dentists, departments, and overall system performance
        """
        try:
            # Top performing dentists
            top_dentists = Dentist.objects.annotate(
                case_count=Count('dentist_cases', filter=Q(
                    dentist_cases__created_at__date__gte=self.start_date
                )),
                completed_cases=Count('dentist_cases', filter=Q(
                    dentist_cases__status__in=['completed', 'delivered'],
                    dentist_cases__created_at__date__gte=self.start_date
                )),
                avg_case_value=Coalesce(Avg('dentist_cases__invoice__total_amount'), Decimal('0.00')),
                total_revenue=Coalesce(Sum('dentist_cases__invoice__total_amount'), Decimal('0.00'))
            ).filter(case_count__gt=0).order_by('-total_revenue')[:10]

            # Department performance
            department_performance = Department.objects.annotate(
                cases_handled=Count('responsible_cases', filter=Q(responsible_cases__created_at__gte=self.start_date)),
                cases_completed=Count('responsible_cases', filter=Q(
                    responsible_cases__status__in=['completed', 'delivered'],
                    responsible_cases__created_at__gte=self.start_date
                )),
                avg_completion_time=Avg(
                    ExpressionWrapper(
                        F('responsible_cases__actual_completion') - F('responsible_cases__created_at'),
                        output_field=IntegerField()
                    ),
                    filter=Q(
                        responsible_cases__status__in=['completed', 'delivered'],
                        responsible_cases__actual_completion__isnull=False,
                        responsible_cases__created_at__gte=self.start_date
                    )
                ),
                completion_rate=ExpressionWrapper(
                    Count('responsible_cases', filter=Q(
                        responsible_cases__status__in=['completed', 'delivered'],
                        responsible_cases__created_at__gte=self.start_date
                    )) * 100.0 / Count('responsible_cases', filter=Q(responsible_cases__created_at__gte=self.start_date)),
                    output_field=DecimalField(max_digits=5, decimal_places=2)
                )
            ).filter(cases_handled__gt=0).order_by('-completion_rate')

            # User performance (if applicable)
            user_performance = []
            if self.user and hasattr(self.user, 'departments'):
                user_departments = self.user.departments.all()
                for dept_relation in user_departments:
                    dept = dept_relation.department
                    user_performance.append({
                        'department': dept.name,
                        'role': dept_relation.role,
                        'access_level': dept_relation.access_level,
                        'performance_rating': float(dept_relation.performance_rating or 0),
                        'completed_tasks': dept_relation.completed_tasks,
                    })

            return {
                'top_dentists': [
                    {
                        'name': dentist.get_full_name(),
                        'clinic': dentist.clinic_name,
                        'case_count': dentist.case_count,
                        'completed_cases': dentist.completed_cases,
                        'avg_case_value': float(dentist.avg_case_value),
                        'total_revenue': float(dentist.total_revenue),
                        'completion_rate': (dentist.completed_cases / dentist.case_count * 100) if dentist.case_count > 0 else 0
                    }
                    for dentist in top_dentists
                ],
                'department_performance': [
                    {
                        'name': dept.name,
                        'cases_handled': dept.cases_handled,
                        'cases_completed': dept.cases_completed,
                        'avg_completion_days': (dept.avg_completion_time / (24 * 60 * 60)) if dept.avg_completion_time else 0,
                        'completion_rate': float(dept.completion_rate or 0),
                        'capacity_utilization': self._calculate_department_utilization(dept)
                    }
                    for dept in department_performance
                ],
                'user_performance': user_performance,
                'overall_performance_score': self._calculate_overall_performance_score(),
            }

        except Exception as e:
            logger.error(f"Error calculating performance analytics: {e}")
            return {'top_dentists': [], 'department_performance': [], 'user_performance': [], 'overall_performance_score': 50}

    def _calculate_efficiency_score(self, on_time_rate: float, quality_rate: float) -> int:
        """Calculate overall efficiency score"""
        return min(100, max(0, int((on_time_rate * 0.6) + (quality_rate * 0.4))))

    def _calculate_department_utilization(self, department) -> float:
        """Calculate department capacity utilization"""
        try:
            active_cases = department.responsible_cases.filter(
                status__in=['in_progress', 'on_hold']
            ).count()
            return (active_cases / department.capacity * 100) if department.capacity > 0 else 0
        except Exception:
            return 0.0

    def _calculate_overall_performance_score(self) -> int:
        """Calculate overall system performance score"""
        try:
            # Get key metrics
            operational = self.get_operational_metrics()
            financial = self.get_financial_overview()

            # Weight different factors
            efficiency_weight = 0.3
            quality_weight = 0.3
            financial_weight = 0.4

            efficiency_score = operational.get('efficiency_score', 50)
            quality_score = operational.get('quality_rate', 50)
            financial_score = financial.get('financial_health_score', 50)

            overall_score = (
                efficiency_score * efficiency_weight +
                quality_score * quality_weight +
                financial_score * financial_weight
            )

            return min(100, max(0, int(overall_score)))

        except Exception:
            return 50

    def _get_operational_fallback(self) -> Dict[str, Any]:
        """Fallback operational data on error"""
        return {
            'total_cases': 0,
            'cases_today': 0,
            'cases_this_week': 0,
            'cases_this_month': 0,
            'status_distribution': [],
            'priority_distribution': [],
            'avg_completion_days': 0.0,
            'on_time_delivery_rate': 0.0,
            'overdue_cases': 0,
            'ready_to_ship': 0,
            'department_workload': [],
            'quality_check_cases': 0,
            'rework_cases': 0,
            'quality_rate': 100.0,
            'efficiency_score': 50,
        }

    def get_trend_analysis(self) -> Dict[str, Any]:
        """
        Advanced trend analysis for cases, revenue, and performance over time
        """
        try:
            # Case volume trends (daily for last 30 days)
            daily_cases = Case.objects.filter(
                created_at__date__gte=self.start_date
            ).extra(
                select={'day': 'date(created_at)'}
            ).values('day').annotate(
                count=Count('case_number')
            ).order_by('day')

            # Revenue trends (weekly for last 12 weeks)
            weekly_revenue = Invoice.objects.filter(
                date__gte=self.today - timedelta(weeks=12)
            ).annotate(
                week=TruncWeek('date')
            ).values('week').annotate(
                revenue=Coalesce(Sum('total_amount'), Decimal('0.00'))
            ).order_by('week')

            # Monthly trends (last 12 months)
            monthly_trends = Case.objects.filter(
                created_at__date__gte=self.today - timedelta(days=365)
            ).annotate(
                month=TruncMonth('created_at')
            ).values('month').annotate(
                cases=Count('case_number'),
                completed=Count('case_number', filter=Q(status__in=['completed', 'delivered'])),
                revenue=Coalesce(Sum('invoice__total_amount'), Decimal('0.00'))
            ).order_by('month')

            # Department trends
            department_trends = Department.objects.annotate(
                current_month_cases=Count('responsible_cases', filter=Q(
                    responsible_cases__created_at__gte=self.start_of_month
                )),
                last_month_cases=Count('responsible_cases', filter=Q(
                    responsible_cases__created_at__gte=self.start_of_month - timedelta(days=30),
                    responsible_cases__created_at__lt=self.start_of_month
                ))
            ).annotate(
                trend=ExpressionWrapper(
                    (F('current_month_cases') - F('last_month_cases')) * 100.0 /
                    DjangoCase(
                        When(last_month_cases=0, then=Value(1)),
                        default=F('last_month_cases')
                    ),
                    output_field=DecimalField(max_digits=10, decimal_places=2)
                )
            ).order_by('-trend')

            return {
                'daily_cases': [
                    {
                        'date': item['day'].strftime('%Y-%m-%d'),
                        'count': item['count']
                    }
                    for item in daily_cases
                ],
                'weekly_revenue': [
                    {
                        'week': item['week'].strftime('%Y-%m-%d'),
                        'revenue': float(item['revenue'])
                    }
                    for item in weekly_revenue
                ],
                'monthly_trends': [
                    {
                        'month': item['month'].strftime('%Y-%m'),
                        'cases': item['cases'],
                        'completed': item['completed'],
                        'revenue': float(item['revenue']),
                        'completion_rate': (item['completed'] / item['cases'] * 100) if item['cases'] > 0 else 0
                    }
                    for item in monthly_trends
                ],
                'department_trends': [
                    {
                        'name': dept.name,
                        'current_month': dept.current_month_cases,
                        'last_month': dept.last_month_cases,
                        'trend_percentage': float(dept.trend or 0)
                    }
                    for dept in department_trends
                ]
            }

        except Exception as e:
            logger.error(f"Error calculating trend analysis: {e}")
            return {'daily_cases': [], 'weekly_revenue': [], 'monthly_trends': [], 'department_trends': []}

    def get_predictive_insights(self) -> Dict[str, Any]:
        """
        Predictive insights and forecasting based on historical data
        """
        try:
            # Predict next month's case volume based on trend
            last_3_months = Case.objects.filter(
                created_at__date__gte=self.today - timedelta(days=90)
            ).annotate(
                month=TruncMonth('created_at')
            ).values('month').annotate(
                count=Count('case_number')
            ).order_by('month')

            if len(last_3_months) >= 2:
                # Simple linear trend prediction
                recent_counts = [item['count'] for item in last_3_months]
                avg_growth = sum(recent_counts[i] - recent_counts[i-1] for i in range(1, len(recent_counts))) / (len(recent_counts) - 1)
                predicted_cases = max(0, recent_counts[-1] + avg_growth)
            else:
                predicted_cases = 0

            # Predict revenue based on average case value
            avg_case_value = Invoice.objects.filter(
                date__gte=self.start_date
            ).aggregate(
                avg=Coalesce(Avg('total_amount'), Decimal('0.00'))
            )['avg']

            predicted_revenue = predicted_cases * float(avg_case_value)

            # Identify potential bottlenecks
            bottlenecks = Department.objects.annotate(
                utilization=ExpressionWrapper(
                    Count('responsible_cases', filter=Q(responsible_cases__status__in=['in_progress', 'on_hold'])) * 100.0 / F('capacity'),
                    output_field=DecimalField(max_digits=5, decimal_places=2)
                )
            ).filter(utilization__gte=80).order_by('-utilization')

            # Seasonal patterns (if enough data)
            seasonal_data = Case.objects.filter(
                created_at__date__gte=self.today - timedelta(days=365)
            ).annotate(
                month_num=Extract('created_at', 'month')
            ).values('month_num').annotate(
                avg_cases=Avg('case_number')
            ).order_by('month_num')

            return {
                'predicted_next_month_cases': int(predicted_cases),
                'predicted_next_month_revenue': round(predicted_revenue, 2),
                'potential_bottlenecks': [
                    {
                        'department': dept.name,
                        'utilization': float(dept.utilization or 0),
                        'capacity': dept.capacity,
                        'recommendation': self._get_bottleneck_recommendation(dept.utilization or 0)
                    }
                    for dept in bottlenecks
                ],
                'seasonal_patterns': list(seasonal_data),
                'growth_trend': self._analyze_growth_trend(),
                'risk_factors': self._identify_risk_factors(),
            }

        except Exception as e:
            logger.error(f"Error calculating predictive insights: {e}")
            return {
                'predicted_next_month_cases': 0,
                'predicted_next_month_revenue': 0.0,
                'potential_bottlenecks': [],
                'seasonal_patterns': [],
                'growth_trend': 'stable',
                'risk_factors': [],
            }

    def get_quality_metrics(self) -> Dict[str, Any]:
        """
        Quality control metrics and indicators
        """
        try:
            # Quality control cases
            quality_cases = Case.objects.filter(
                created_at__date__gte=self.start_date
            )

            total_cases = quality_cases.count()

            # Rework statistics
            rework_cases = quality_cases.filter(status='rework_required').count()
            quality_check_cases = quality_cases.filter(status='quality_check').count()

            # First-pass yield (cases that don't require rework)
            first_pass_yield = ((total_cases - rework_cases) / total_cases * 100) if total_cases > 0 else 100

            # Average time in quality check
            avg_quality_time = Case.objects.filter(
                status='quality_check',
                created_at__date__gte=self.start_date
            ).aggregate(
                avg_time=Avg(
                    ExpressionWrapper(
                        timezone.now() - F('created_at'),
                        output_field=IntegerField()
                    )
                )
            )['avg_time']

            avg_quality_hours = (avg_quality_time / 3600) if avg_quality_time else 0

            # Quality by department
            department_quality = Department.objects.annotate(
                total_cases=Count('responsible_cases', filter=Q(responsible_cases__created_at__gte=self.start_date)),
                rework_cases=Count('responsible_cases', filter=Q(
                    responsible_cases__status='rework_required',
                    responsible_cases__created_at__gte=self.start_date
                )),
                quality_rate=ExpressionWrapper(
                    (Count('responsible_cases', filter=Q(responsible_cases__created_at__gte=self.start_date)) -
                     Count('responsible_cases', filter=Q(
                         responsible_cases__status='rework_required',
                         responsible_cases__created_at__gte=self.start_date
                     ))) * 100.0 /
                    DjangoCase(
                        When(responsible_cases__created_at__gte=self.start_date, then=Count('responsible_cases')),
                        default=Value(1)
                    ),
                    output_field=DecimalField(max_digits=5, decimal_places=2)
                )
            ).filter(total_cases__gt=0).order_by('-quality_rate')

            return {
                'total_cases_reviewed': total_cases,
                'rework_cases': rework_cases,
                'quality_check_cases': quality_check_cases,
                'first_pass_yield': round(first_pass_yield, 1),
                'avg_quality_check_hours': round(avg_quality_hours, 1),
                'rework_rate': round((rework_cases / total_cases * 100) if total_cases > 0 else 0, 1),
                'department_quality': [
                    {
                        'name': dept.name,
                        'total_cases': dept.total_cases,
                        'rework_cases': dept.rework_cases,
                        'quality_rate': float(dept.quality_rate or 100)
                    }
                    for dept in department_quality
                ],
                'quality_score': self._calculate_quality_score(first_pass_yield, avg_quality_hours),
            }

        except Exception as e:
            logger.error(f"Error calculating quality metrics: {e}")
            return {
                'total_cases_reviewed': 0,
                'rework_cases': 0,
                'quality_check_cases': 0,
                'first_pass_yield': 100.0,
                'avg_quality_check_hours': 0.0,
                'rework_rate': 0.0,
                'department_quality': [],
                'quality_score': 100,
            }

    def _get_bottleneck_recommendation(self, utilization: float) -> str:
        """Get recommendation based on department utilization"""
        if utilization >= 95:
            return "Critical: Consider immediate capacity expansion or workload redistribution"
        elif utilization >= 85:
            return "High: Monitor closely and prepare for capacity adjustments"
        elif utilization >= 75:
            return "Moderate: Optimize workflow efficiency"
        else:
            return "Normal: Maintain current operations"

    def _analyze_growth_trend(self) -> str:
        """Analyze overall growth trend"""
        try:
            current_month = Case.objects.filter(
                created_at__date__gte=self.start_of_month
            ).count()

            last_month = Case.objects.filter(
                created_at__date__gte=self.start_of_month - timedelta(days=30),
                created_at__date__lt=self.start_of_month
            ).count()

            if last_month == 0:
                return 'insufficient_data'

            growth_rate = (current_month - last_month) / last_month * 100

            if growth_rate > 10:
                return 'strong_growth'
            elif growth_rate > 5:
                return 'moderate_growth'
            elif growth_rate > -5:
                return 'stable'
            elif growth_rate > -10:
                return 'moderate_decline'
            else:
                return 'strong_decline'

        except Exception:
            return 'stable'

    def _identify_risk_factors(self) -> List[Dict[str, str]]:
        """Identify potential risk factors"""
        risks = []

        try:
            # High overdue rate
            overdue_rate = Invoice.objects.filter(
                status__in=['unpaid', 'partial'],
                due_date__lt=self.today
            ).count() / max(1, Invoice.objects.filter(status__in=['unpaid', 'partial']).count()) * 100

            if overdue_rate > 30:
                risks.append({
                    'type': 'financial',
                    'description': f'High overdue invoice rate: {overdue_rate:.1f}%',
                    'severity': 'high' if overdue_rate > 50 else 'medium'
                })

            # High rework rate
            rework_rate = Case.objects.filter(
                status='rework_required',
                created_at__date__gte=self.start_date
            ).count() / max(1, Case.objects.filter(created_at__date__gte=self.start_date).count()) * 100

            if rework_rate > 10:
                risks.append({
                    'type': 'quality',
                    'description': f'High rework rate: {rework_rate:.1f}%',
                    'severity': 'high' if rework_rate > 20 else 'medium'
                })

            # Department overutilization
            overutilized_depts = Department.objects.annotate(
                utilization=Count('cases', filter=Q(cases__status__in=['in_progress', 'on_hold'])) * 100.0 / F('capacity')
            ).filter(utilization__gte=90).count()

            if overutilized_depts > 0:
                risks.append({
                    'type': 'operational',
                    'description': f'{overutilized_depts} department(s) over 90% capacity',
                    'severity': 'medium'
                })

        except Exception as e:
            logger.error(f"Error identifying risk factors: {e}")

        return risks

    def _calculate_quality_score(self, first_pass_yield: float, avg_quality_hours: float) -> int:
        """Calculate overall quality score"""
        # Weight first pass yield more heavily
        yield_score = first_pass_yield

        # Penalize long quality check times (assume 4 hours is optimal)
        time_score = max(0, 100 - (avg_quality_hours - 4) * 5) if avg_quality_hours > 4 else 100

        overall_score = (yield_score * 0.7) + (time_score * 0.3)
        return min(100, max(0, int(overall_score)))

    def get_real_time_alerts(self) -> List[Dict[str, Any]]:
        """
        Get real-time alerts and notifications for immediate attention
        """
        alerts = []

        try:
            # Critical overdue cases
            critical_overdue = Case.objects.filter(
                deadline__lt=timezone.now() - timedelta(days=2),
                status__in=['pending_acceptance', 'in_progress', 'on_hold']
            ).count()

            if critical_overdue > 0:
                alerts.append({
                    'type': 'critical',
                    'category': 'deadline',
                    'title': 'Critical Overdue Cases',
                    'message': f'{critical_overdue} cases are more than 2 days overdue',
                    'count': critical_overdue,
                    'action_url': '/case/?status=overdue',
                    'priority': 1
                })

            # High-value overdue invoices
            high_value_overdue = Invoice.objects.filter(
                status__in=['unpaid', 'partial'],
                due_date__lt=self.today,
                total_amount__gte=1000
            ).aggregate(
                count=Count('id'),
                total=Coalesce(Sum('total_amount'), Decimal('0.00'))
            )

            if high_value_overdue['count'] > 0:
                alerts.append({
                    'type': 'warning',
                    'category': 'financial',
                    'title': 'High-Value Overdue Invoices',
                    'message': f'${high_value_overdue["total"]:,.2f} in {high_value_overdue["count"]} overdue invoices',
                    'count': high_value_overdue['count'],
                    'amount': float(high_value_overdue['total']),
                    'action_url': '/billing/?status=overdue',
                    'priority': 2
                })

            # Department capacity warnings
            overloaded_departments = Department.objects.annotate(
                current_load=Count('responsible_cases', filter=Q(responsible_cases__status__in=['in_progress', 'on_hold'])),
                utilization=ExpressionWrapper(
                    Count('responsible_cases', filter=Q(responsible_cases__status__in=['in_progress', 'on_hold'])) * 100.0 / F('capacity'),
                    output_field=DecimalField(max_digits=5, decimal_places=2)
                )
            ).filter(utilization__gte=90)

            for dept in overloaded_departments:
                alerts.append({
                    'type': 'warning',
                    'category': 'capacity',
                    'title': f'{dept.name} Over Capacity',
                    'message': f'Running at {dept.utilization:.1f}% capacity ({dept.current_load}/{dept.capacity})',
                    'department': dept.name,
                    'utilization': float(dept.utilization),
                    'action_url': f'/case/?department={dept.id}',
                    'priority': 3
                })

            # Quality issues
            recent_rework = Case.objects.filter(
                status='rework_required',
                created_at__date__gte=self.today - timedelta(days=7)
            ).count()

            if recent_rework > 5:
                alerts.append({
                    'type': 'info',
                    'category': 'quality',
                    'title': 'Increased Rework Cases',
                    'message': f'{recent_rework} cases required rework in the last 7 days',
                    'count': recent_rework,
                    'action_url': '/case/?status=rework_required',
                    'priority': 4
                })

            # Sort by priority
            alerts.sort(key=lambda x: x['priority'])

        except Exception as e:
            logger.error(f"Error getting real-time alerts: {e}")

        return alerts

    def get_current_workload(self) -> Dict[str, Any]:
        """
        Get current workload distribution and status
        """
        try:
            # Overall workload
            total_active = Case.objects.filter(
                status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
            ).count()

            # By status
            workload_by_status = Case.objects.filter(
                status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check', 'ready_to_ship']
            ).values('status').annotate(
                count=Count('case_number')
            ).order_by('-count')

            # By department
            workload_by_department = Department.objects.annotate(
                active_cases=Count('responsible_cases', filter=Q(
                    responsible_cases__status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
                )),
                capacity_used=ExpressionWrapper(
                    Count('responsible_cases', filter=Q(
                        responsible_cases__status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
                    )) * 100.0 / F('capacity'),
                    output_field=DecimalField(max_digits=5, decimal_places=2)
                )
            ).filter(active_cases__gt=0).order_by('-capacity_used')

            # By priority
            workload_by_priority = Case.objects.filter(
                status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
            ).values('priority').annotate(
                count=Count('case_number')
            ).order_by('priority')

            # Upcoming deadlines (next 7 days)
            upcoming_deadlines = Case.objects.filter(
                deadline__gte=timezone.now(),
                deadline__lte=timezone.now() + timedelta(days=7),
                status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
            ).order_by('deadline')[:10]

            return {
                'total_active_cases': total_active,
                'by_status': list(workload_by_status),
                'by_department': [
                    {
                        'name': dept.name,
                        'active_cases': dept.active_cases,
                        'capacity': dept.capacity,
                        'utilization': float(dept.capacity_used or 0),
                        'status': self._get_capacity_status(dept.capacity_used or 0)
                    }
                    for dept in workload_by_department
                ],
                'by_priority': list(workload_by_priority),
                'upcoming_deadlines': [
                    {
                        'case_number': case.case_number,
                        'dentist': case.dentist.get_full_name() if case.dentist else 'N/A',
                        'deadline': case.deadline,
                        'days_remaining': (case.deadline.date() - self.today).days,
                        'status': case.status,
                        'priority': case.priority
                    }
                    for case in upcoming_deadlines
                ],
                'workload_score': self._calculate_workload_score(total_active),
            }

        except Exception as e:
            logger.error(f"Error getting current workload: {e}")
            return {
                'total_active_cases': 0,
                'by_status': [],
                'by_department': [],
                'by_priority': [],
                'upcoming_deadlines': [],
                'workload_score': 50,
            }

    def get_comprehensive_chart_data(self) -> Dict[str, Any]:
        """
        Get all chart data for visualizations
        """
        try:
            # Use a longer period for trends (at least 12 months or the selected range)
            chart_start_date = self.start_date
            chart_end_date = self.end_date if hasattr(self, 'end_date') else self.today

            # Ensure we have at least 12 months of data for meaningful trends
            min_start_date = self.today - timedelta(days=365)
            if chart_start_date > min_start_date:
                chart_start_date = min_start_date

            # Revenue vs Cases chart (monthly)
            # Note: created_at is a DateTimeField, so we need to handle datetime filtering
            from django.utils import timezone as django_timezone

            # Convert dates to datetime for filtering
            if hasattr(chart_start_date, 'date'):
                # It's already a datetime
                start_datetime = chart_start_date
            else:
                # It's a date, convert to datetime
                start_datetime = django_timezone.make_aware(
                    datetime.combine(chart_start_date, datetime.min.time())
                )

            if hasattr(chart_end_date, 'date'):
                # It's already a datetime
                end_datetime = chart_end_date
            else:
                # It's a date, convert to datetime
                end_datetime = django_timezone.make_aware(
                    datetime.combine(chart_end_date, datetime.max.time())
                )

            monthly_data = Case.objects.filter(
                created_at__gte=start_datetime,
                created_at__lte=end_datetime
            ).annotate(
                month=TruncMonth('created_at')
            ).values('month').annotate(
                cases=Count('case_number'),
                revenue=Coalesce(Sum('invoice__total_amount'), Decimal('0.00'))
            ).order_by('month')

            # Department performance radar chart
            dept_performance = Department.objects.annotate(
                efficiency=ExpressionWrapper(
                    Count('responsible_cases', filter=Q(
                        responsible_cases__status__in=['completed', 'delivered'],
                        responsible_cases__actual_completion__lte=F('responsible_cases__deadline')
                    )) * 100.0 / Count('responsible_cases', filter=Q(responsible_cases__status__in=['completed', 'delivered'])),
                    output_field=DecimalField(max_digits=5, decimal_places=2)
                ),
                quality=ExpressionWrapper(
                    (Count('responsible_cases') - Count('responsible_cases', filter=Q(responsible_cases__status='rework_required'))) * 100.0 / Count('responsible_cases'),
                    output_field=DecimalField(max_digits=5, decimal_places=2)
                ),
                productivity=ExpressionWrapper(
                    Count('responsible_cases', filter=Q(responsible_cases__created_at__gte=self.start_of_month)) * 100.0 / F('capacity'),
                    output_field=DecimalField(max_digits=5, decimal_places=2)
                )
            ).filter(responsible_cases__isnull=False)

            # Status distribution pie chart
            status_distribution = Case.objects.filter(
                created_at__date__gte=self.start_date
            ).values('status').annotate(
                count=Count('case_number')
            ).order_by('-count')

            # Priority distribution
            priority_distribution = Case.objects.filter(
                created_at__date__gte=self.start_date
            ).values('priority').annotate(
                count=Count('case_number')
            ).order_by('priority')

            # Debug: Log the query and results
            logger.info(f"Chart data query from {start_datetime} to {end_datetime}")

            # Check if we have any cases at all
            total_cases = Case.objects.count()
            total_departments = Department.objects.count()
            total_invoices = Invoice.objects.count()
            logger.info(f"Database stats - Cases: {total_cases}, Departments: {total_departments}, Invoices: {total_invoices}")

            monthly_data_list = list(monthly_data)
            logger.info(f"Monthly data count: {len(monthly_data_list)}")
            if monthly_data_list:
                logger.info(f"Sample monthly data: {monthly_data_list[:3]}")  # Show first 3 items
            else:
                logger.info("No monthly data found")

            # If no data, create some sample data for demonstration
            if not monthly_data_list and total_cases == 0:
                logger.info("No cases found, creating sample data")
                # Generate sample data for the last 6 months
                sample_data = []
                for i in range(6):
                    month_date = (self.today.replace(day=1) - timedelta(days=30*i))
                    sample_data.append({
                        'month': month_date.strftime('%Y-%m'),
                        'cases': 0,
                        'revenue': 0.0
                    })
                sample_data.reverse()  # Chronological order

                return {
                    'monthly_revenue_cases': sample_data,
                    'department_performance': [],
                    'status_distribution': [],
                    'priority_distribution': [],
                    'debug_info': {
                        'total_cases': total_cases,
                        'query_range': f"{start_datetime} to {end_datetime}",
                        'sample_data': True
                    }
                }

            return {
                'monthly_revenue_cases': [
                    {
                        'month': item['month'].strftime('%Y-%m'),
                        'cases': item['cases'],
                        'revenue': float(item['revenue'])
                    }
                    for item in monthly_data_list
                ],
                'department_performance': [
                    {
                        'name': dept.name,
                        'efficiency': float(dept.efficiency or 0),
                        'quality': float(dept.quality or 100),
                        'productivity': float(dept.productivity or 0)
                    }
                    for dept in dept_performance
                ],
                'status_distribution': list(status_distribution),
                'priority_distribution': list(priority_distribution),
                'debug_info': {
                    'total_cases': total_cases,
                    'query_range': f"{start_datetime} to {end_datetime}",
                    'monthly_data_count': len(monthly_data_list),
                    'sample_data': False
                }
            }

        except Exception as e:
            logger.error(f"Error getting chart data: {e}")
            return {
                'monthly_revenue_cases': [],
                'department_performance': [],
                'status_distribution': [],
                'priority_distribution': [],
            }

    def _get_capacity_status(self, utilization: float) -> str:
        """Get capacity status based on utilization"""
        if utilization >= 95:
            return 'critical'
        elif utilization >= 85:
            return 'high'
        elif utilization >= 70:
            return 'moderate'
        else:
            return 'normal'

    def _calculate_workload_score(self, total_active: int) -> int:
        """Calculate workload management score"""
        # This is a simplified scoring system
        # You can adjust based on your business rules
        total_capacity = Department.objects.aggregate(
            total=Coalesce(Sum('capacity'), 0)
        )['total']

        if total_capacity == 0:
            return 50

        utilization = (total_active / total_capacity) * 100

        if utilization <= 70:
            return 100
        elif utilization <= 85:
            return 80
        elif utilization <= 95:
            return 60
        else:
            return 30

    def get_recent_activities(self) -> List[Dict[str, Any]]:
        """
        Get recent activities and system events
        """
        activities = []

        try:
            # Recent case creations
            recent_cases = Case.objects.filter(
                created_at__gte=timezone.now() - timedelta(hours=24)
            ).order_by('-created_at')[:5]

            for case in recent_cases:
                activities.append({
                    'type': 'case_created',
                    'title': f'New Case #{case.case_number}',
                    'description': f'Case created for {case.dentist.get_full_name() if case.dentist else "Unknown"}',
                    'timestamp': case.created_at,
                    'icon': 'plus-circle',
                    'color': 'success',
                    'url': f'/case/{case.case_number}/'
                })

            # Recent completions
            recent_completions = Case.objects.filter(
                status__in=['completed', 'delivered'],
                actual_completion__gte=timezone.now() - timedelta(hours=24)
            ).order_by('-actual_completion')[:5]

            for case in recent_completions:
                activities.append({
                    'type': 'case_completed',
                    'title': f'Case #{case.case_number} Completed',
                    'description': f'Completed for {case.dentist.get_full_name() if case.dentist else "Unknown"}',
                    'timestamp': case.actual_completion,
                    'icon': 'check-circle',
                    'color': 'primary',
                    'url': f'/case/{case.case_number}/'
                })

            # Recent payments
            recent_payments = Payment.objects.filter(
                date__gte=timezone.now() - timedelta(hours=24)
            ).order_by('-date')[:5]

            for payment in recent_payments:
                activities.append({
                    'type': 'payment_received',
                    'title': f'Payment Received: ${payment.amount:,.2f}',
                    'description': f'Payment method: {payment.payment_method}',
                    'timestamp': timezone.make_aware(datetime.combine(payment.date, datetime.min.time())),
                    'icon': 'credit-card',
                    'color': 'success',
                    'url': f'/finance/payments/{payment.id}/'
                })

            # Sort all activities by timestamp
            activities.sort(key=lambda x: x['timestamp'], reverse=True)

            # Return top 10 most recent
            return activities[:10]

        except Exception as e:
            logger.error(f"Error getting recent activities: {e}")
            return []

    def get_upcoming_deadlines(self) -> List[Dict[str, Any]]:
        """
        Get upcoming deadlines and important dates
        """
        try:
            # Cases due in next 7 days
            upcoming_cases = Case.objects.filter(
                deadline__gte=timezone.now(),
                deadline__lte=timezone.now() + timedelta(days=7),
                status__in=['pending_acceptance', 'in_progress', 'on_hold', 'quality_check']
            ).order_by('deadline')[:15]

            deadlines = []
            for case in upcoming_cases:
                days_remaining = (case.deadline.date() - self.today).days
                urgency = 'critical' if days_remaining <= 1 else 'warning' if days_remaining <= 3 else 'info'

                deadlines.append({
                    'type': 'case_deadline',
                    'case_number': case.case_number,
                    'title': f'Case #{case.case_number} Due',
                    'description': f'Due for {case.dentist.get_full_name() if case.dentist else "Unknown"}',
                    'deadline': case.deadline,
                    'days_remaining': days_remaining,
                    'urgency': urgency,
                    'status': case.status,
                    'priority': case.priority,
                    'url': f'/case/{case.case_number}/'
                })

            # Invoice due dates
            upcoming_invoices = Invoice.objects.filter(
                due_date__gte=self.today,
                due_date__lte=self.today + timedelta(days=7),
                status__in=['unpaid', 'partial']
            ).order_by('due_date')[:10]

            for invoice in upcoming_invoices:
                days_remaining = (invoice.due_date - self.today).days
                urgency = 'critical' if days_remaining <= 1 else 'warning' if days_remaining <= 3 else 'info'

                deadlines.append({
                    'type': 'invoice_due',
                    'invoice_number': invoice.invoice_number,
                    'title': f'Invoice #{invoice.invoice_number} Due',
                    'description': f'Amount: ${invoice.total_amount:,.2f}',
                    'deadline': timezone.make_aware(datetime.combine(invoice.due_date, datetime.min.time())),
                    'days_remaining': days_remaining,
                    'urgency': urgency,
                    'amount': float(invoice.total_amount),
                    'url': f'/billing/invoices/{invoice.id}/'
                })

            # Sort by deadline
            deadlines.sort(key=lambda x: x['deadline'])

            return deadlines

        except Exception as e:
            logger.error(f"Error getting upcoming deadlines: {e}")
            return []
