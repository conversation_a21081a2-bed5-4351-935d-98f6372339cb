"""URL configuration for LAB project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from django.shortcuts import redirect
from LAB import settings
from accounts.views.main import home, home_vue, home_debug, test_view, context_debug, template_debug
from accounts.views.ultimate_dashboard_views import ultimate_home_view
from django.conf import settings
from django.conf.urls.static import static
import debug_toolbar

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('accounts.urls')),
    path('', ultimate_home_view, name='home'),
    path('home-legacy/', home, name='home_legacy'),
    path('home-vue/', home_vue, name='home_vue'),
    path('home-debug/', home_debug, name='home_debug'),
    path('test/', test_view, name='test_view'),
    path('context-debug/', context_debug, name='context_debug'),
    path('template-debug/', template_debug, name='template_debug'),
    path('__debug__/', include(debug_toolbar.urls)),
    path('dashboard/', include([
        path('', lambda request: redirect('accounts:dashboard_redirect'), name='dashboard'),
    ])),
    path('Dentists/', include('Dentists.urls', namespace='Dentists')),
    path('patients/', include('patients.urls', namespace='patients')),
    path('items/', include('items.urls', namespace='items')),
    # path('invoicing/', include('invoicing.urls')), kjo eshte nga plugini invoicing django qe e kemi shkarkuar
    path('billing/', include('billing.urls', namespace='billing')),
    path('case/', include('case.urls')),
    path('reports/', include('reports.urls')),
    path('finance/', include('finance.urls',namespace='finance')),
    path('scheduling/', include('scheduling.urls', namespace='scheduling')),
    path('ledger/', include('ledger.urls', namespace='ledger')),
    # path('notifications/', include('notifications.urls', namespace='notifications')), # Temporarily commented out due to migration issues
]+ static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Add error handlers
handler403 = 'accounts.views.main.custom_403'
handler404 = 'accounts.views.main.custom_404'
handler500 = 'accounts.views.main.custom_500'
