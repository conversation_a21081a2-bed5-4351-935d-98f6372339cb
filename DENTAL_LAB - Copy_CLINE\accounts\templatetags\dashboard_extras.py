from django import template
from django.utils.safestring import mark_safe
import json

register = template.Library()

@register.filter
def subtract(value, arg):
    """Subtracts the arg from the value."""
    try:
        return int(value) - int(arg)
    except (ValueError, TypeError):
        try:
            return float(value) - float(arg)
        except (ValueError, TypeError):
            return 0

@register.filter
def multiply(value, arg):
    """Multiplies the value by the arg."""
    try:
        return int(value) * int(arg)
    except (ValueError, TypeError):
        try:
            return float(value) * float(arg)
        except (ValueError, TypeError):
            return 0

@register.filter
def to_json(value):
    """Convert a Python object to JSON string"""
    return mark_safe(json.dumps(value))

@register.filter
def percentage(value, total):
    """Calculate percentage"""
    if total == 0:
        return 0
    return round((value / total) * 100, 1)

@register.filter
def currency(value):
    """Format value as currency"""
    try:
        return f"${float(value):,.2f}"
    except (ValueError, TypeError):
        return "$0.00"





@register.filter
def divide(value, arg):
    """Divides the value by the arg."""
    try:
        return int(value) / int(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        try:
            return float(value) / float(arg)
        except (ValueError, TypeError, ZeroDivisionError):
            return 0

@register.filter
def percentage(value, arg):
    """Returns the percentage of value to arg."""
    try:
        return (float(value) / float(arg)) * 100
    except (ValueError, TypeError, ZeroDivisionError):
        return 0
