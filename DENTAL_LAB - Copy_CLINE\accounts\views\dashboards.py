"""
Role-based dashboard views for the Dental Lab application.
This module provides dashboard views for different user roles:
- Administrator
- Manager
- Technical Staff
- Dentist
"""

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.db.models import Count, Sum, Avg, Q, F
from django.utils import timezone
from datetime import datetime, timedelta

from case.models import Case, Task, Department, CaseItem
from accounts.models import CustomUser, UserDepartment
from Dentists.models import Dentist
from billing.models import Invoice
from finance.models import Payment
from ..services.admin_dashboard_service import AdminDashboardService

import logging
logger = logging.getLogger(__name__)

class DashboardRouterView(LoginRequiredMixin, TemplateView):
    """
    Routes users to their appropriate dashboard based on role.
    """
    template_name = 'accounts/dashboards/dashboard_router.html'  # Fallback template

    def dispatch(self, request, *args, **kwargs):
        user = request.user

        # Route based on user type
        if user.is_superuser:
            return redirect('accounts:admin_dashboard')
        elif user.is_staff:
            # Check if user is a department manager
            if user.managed_departments.exists():
                return redirect('accounts:manager_dashboard')
            else:
                return redirect('accounts:staff_dashboard')
        elif user.user_type == 2:  # Dentist
            return redirect('Dentists:dashboard')

        # Fallback to default template if no specific dashboard
        return super().dispatch(request, *args, **kwargs)

class AdminDashboardView(LoginRequiredMixin, UserPassesTestMixin, TemplateView):
    """
    Dashboard for administrators with full system overview using real database data.
    """
    template_name = 'accounts/dashboards/admin_dashboard.html'

    def test_func(self):
        return self.request.user.is_superuser

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        try:
            # Get date range parameter
            date_range_days = int(self.request.GET.get('range', 30))
            if date_range_days not in [7, 30, 90, 180, 365]:
                date_range_days = 30

            # Initialize admin dashboard service
            admin_service = AdminDashboardService(date_range_days)

            # Get comprehensive admin data
            admin_data = admin_service.get_comprehensive_admin_data()

            # Add all data to context
            context.update(admin_data)
            context.update({
                'user': self.request.user,
                'selected_range': str(date_range_days),
                'dashboard_type': 'admin',
                'page_title': 'Admin Dashboard',
            })

            logger.info(f"Admin dashboard loaded successfully for user {self.request.user.id}")

        except Exception as e:
            logger.error(f"Error loading admin dashboard for user {self.request.user.id}: {e}")

            # Fallback context
            context.update({
                'total_users': 0,
                'active_users': 0,
                'staff_count': 0,
                'dentist_count': 0,
                'total_cases': 0,
                'active_cases': 0,
                'cases_this_month': 0,
                'cases_last_month': 0,
                'total_invoices': 0,
                'unpaid_invoices': 0,
                'departments': [],
                'recent_cases': [],
                'recent_invoices': [],
                'chart_data': {},
                'error': True,
                'error_message': 'Unable to load dashboard data. Please try again.',
            })

        return context

class ManagerDashboardView(LoginRequiredMixin, UserPassesTestMixin, TemplateView):
    """
    Dashboard for department managers with focus on team performance and workload.
    """
    template_name = 'accounts/dashboards/manager_dashboard.html'

    def test_func(self):
        # Check if user is staff and manages at least one department
        return self.request.user.is_staff and self.request.user.managed_departments.exists()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        from django.utils import timezone
        from django.db.models import Count, Avg, F, ExpressionWrapper, fields, Q, Sum
        from django.db.models.functions import TruncDay, TruncWeek, TruncMonth
        import json
        from datetime import datetime, timedelta

        today = timezone.now().date()
        thirty_days_ago = today - timedelta(days=30)
        seven_days_ago = today - timedelta(days=7)

        # Get managed departments
        managed_departments = user.managed_departments.all()
        context['managed_departments'] = managed_departments

        # Department statistics with enhanced metrics
        dept_stats = []
        for dept in managed_departments:
            # Basic task counts
            all_dept_tasks = Task.objects.filter(workflow_stage__department=dept)
            active_tasks = all_dept_tasks.filter(status='in_progress')
            completed_tasks = all_dept_tasks.filter(status='completed')
            pending_tasks = all_dept_tasks.filter(status='pending')

            # Overdue tasks
            overdue_tasks = all_dept_tasks.filter(
                Q(status__in=['pending', 'in_progress']) &
                Q(case__deadline__lt=today)
            )

            # Tasks due soon (next 3 days)
            due_soon_tasks = all_dept_tasks.filter(
                Q(status__in=['pending', 'in_progress']) &
                Q(case__deadline__gte=today) &
                Q(case__deadline__lte=today + timedelta(days=3))
            )

            # Task completion time (average days to complete)
            completed_with_duration = completed_tasks.exclude(
                actual_start_time=None
            ).exclude(
                actual_end_time=None
            )

            avg_completion_time = 0
            if completed_with_duration.exists():
                # Calculate average duration in days
                total_days = sum([(task.actual_end_time - task.actual_start_time).total_seconds() / 86400
                                 for task in completed_with_duration])
                avg_completion_time = total_days / completed_with_duration.count()

            # Task completion trend (last 30 days)
            completion_trend = completed_tasks.filter(
                actual_end_time__date__gte=thirty_days_ago
            ).annotate(
                day=TruncDay('actual_end_time')
            ).values('day').annotate(
                count=Count('id')
            ).order_by('day')

            # Convert to list of [date, count] for charting
            completion_trend_data = []
            for item in completion_trend:
                if item['day']:
                    completion_trend_data.append({
                        'date': item['day'].strftime('%Y-%m-%d'),
                        'count': item['count']
                    })

            # Calculate workload distribution
            staff_workload = []
            dept_users = dept.users.select_related('user').filter(is_active=True)

            for staff_user in dept_users:
                active_staff_tasks = active_tasks.filter(assigned_to=staff_user.user).count()
                pending_staff_tasks = pending_tasks.filter(assigned_to=staff_user.user).count()
                staff_workload.append({
                    'name': staff_user.user.get_full_name(),
                    'active': active_staff_tasks,
                    'pending': pending_staff_tasks,
                    'total': active_staff_tasks + pending_staff_tasks
                })

            # Sort by total workload (descending)
            staff_workload.sort(key=lambda x: x['total'], reverse=True)

            # Calculate efficiency metrics
            tasks_completed_30d = completed_tasks.filter(
                actual_end_time__date__gte=thirty_days_ago
            ).count()

            tasks_created_30d = all_dept_tasks.filter(
                created_at__date__gte=thirty_days_ago
            ).count()

            efficiency_ratio = 0
            if tasks_created_30d > 0:
                efficiency_ratio = (tasks_completed_30d / tasks_created_30d) * 100

            # Add all metrics to department stats
            dept_stats.append({
                'department': dept,
                'staff_count': dept_users.count(),
                'active_tasks': active_tasks.count(),
                'completed_tasks': completed_tasks.count(),
                'pending_tasks': pending_tasks.count(),
                'overdue_tasks': overdue_tasks.count(),
                'due_soon_tasks': due_soon_tasks.count(),
                'avg_completion_time': round(avg_completion_time, 1),
                'completion_trend_data': json.dumps(completion_trend_data),
                'staff_workload': staff_workload,
                'efficiency_ratio': round(efficiency_ratio, 1),
                'tasks_completed_30d': tasks_completed_30d,
                'tasks_created_30d': tasks_created_30d,
            })

        context['department_stats'] = dept_stats

        # Staff performance with enhanced metrics
        staff_members = CustomUser.objects.filter(
            departments__department__in=managed_departments,
            is_active=True
        ).prefetch_related('departments').distinct() # Corrected prefetch_related to use 'departments'

        staff_performance = []
        for staff in staff_members:
            # Basic task metrics
            assigned_tasks = Task.objects.filter(assigned_to=staff)
            completed_tasks = assigned_tasks.filter(status='completed')
            active_tasks = assigned_tasks.filter(status='in_progress')
            pending_tasks = assigned_tasks.filter(status='pending')

            # Calculate completion rate
            completion_rate = 0
            if assigned_tasks.count() > 0:
                completion_rate = (completed_tasks.count() / assigned_tasks.count()) * 100

            # Calculate on-time completion rate
            on_time_tasks = completed_tasks.filter(
                Q(actual_end_time__date__lte=F('case__deadline')) |
                Q(case__deadline=None)
            )

            on_time_rate = 0
            if completed_tasks.count() > 0:
                on_time_rate = (on_time_tasks.count() / completed_tasks.count()) * 100

            # Calculate average task duration
            completed_with_duration = completed_tasks.exclude(
                actual_start_time=None
            ).exclude(
                actual_end_time=None
            )

            avg_task_duration = 0
            if completed_with_duration.exists():
                # Calculate average duration in hours
                total_hours = sum([(task.actual_end_time - task.actual_start_time).total_seconds() / 3600
                                  for task in completed_with_duration])
                avg_task_duration = total_hours / completed_with_duration.count()

            # Recent performance (last 7 days)
            recent_completed = completed_tasks.filter(
                actual_end_time__date__gte=seven_days_ago
            ).count()

            # Task complexity distribution
            task_complexity = {
                'low': assigned_tasks.filter(priority=1).count(),
                'medium': assigned_tasks.filter(priority=2).count(),
                'high': assigned_tasks.filter(priority=3).count(),
                'urgent': assigned_tasks.filter(priority=4).count()
            }

            staff_performance.append({
                'staff': staff,
                'assigned_tasks': assigned_tasks.count(),
                'completed_tasks': completed_tasks.count(),
                'active_tasks': active_tasks.count(),
                'pending_tasks': pending_tasks.count(),
                'completion_rate': round(completion_rate, 1),
                'on_time_rate': round(on_time_rate, 1),
                'avg_task_duration': round(avg_task_duration, 1),
                'recent_completed': recent_completed,
                'task_complexity': task_complexity,
                'departments': staff.departments.all()
            })

        # Sort by completion rate (descending)
        staff_performance.sort(key=lambda x: x['completion_rate'], reverse=True)
        context['staff_performance'] = staff_performance

        # Task status distribution for all managed departments
        task_status_distribution = Task.objects.filter(
            workflow_stage__department__in=managed_departments
        ).values('status').annotate(
            count=Count('id')
        ).order_by('status')

        # Format for Chart.js
        status_labels = [Task.STATUS_CHOICES_DICT.get(item['status'], item['status']) for item in task_status_distribution]
        status_data = [item['count'] for item in task_status_distribution]
        context['task_status_distribution'] = {'labels': status_labels, 'data': status_data}


        # Task priority distribution
        task_priority_distribution = Task.objects.filter(
            workflow_stage__department__in=managed_departments
        ).values('priority').annotate(
            count=Count('id')
        ).order_by('priority')

        # Format for Chart.js
        priority_labels = [Task.PRIORITY_CHOICES_DICT.get(item['priority'], f"Priority {item['priority']}") for item in task_priority_distribution]
        priority_data = [item['count'] for item in task_priority_distribution]
        context['task_priority_distribution'] = {'labels': priority_labels, 'data': priority_data}


        # Task creation trend (last 30 days)
        task_creation_trend = Task.objects.filter(
            workflow_stage__department__in=managed_departments,
            created_at__date__gte=thirty_days_ago
        ).annotate(
            day=TruncDay('created_at')
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')

        # Convert to list for charting
        task_creation_data = []
        for item in task_creation_trend:
            if item['day']:
                task_creation_data.append({
                    'date': item['day'].strftime('%Y-%m-%d'),
                    'count': item['count']
                })

        context['task_creation_trend'] = json.dumps(task_creation_data)

        # Recent tasks with enhanced filtering
        recent_tasks = Task.objects.filter(
            workflow_stage__department__in=managed_departments
        ).select_related('case', 'assigned_to', 'workflow_stage').order_by('-created_at')[:15]

        context['recent_tasks'] = recent_tasks

        # Overdue tasks across all departments
        context['overdue_tasks'] = Task.objects.filter(
            workflow_stage__department__in=managed_departments,
            status__in=['pending', 'in_progress'],
            case__deadline__lt=today
        ).select_related('case', 'assigned_to').order_by('case__deadline')[:10]

        # Cases in managed departments with enhanced data
        department_cases = Case.objects.filter(
            tasks__workflow_stage__department__in=managed_departments
        ).distinct().select_related('patient', 'dentist').order_by('-created_at')[:10]

        context['department_cases'] = department_cases

        # Add date context for filtering
        context['today'] = today
        context['thirty_days_ago'] = thirty_days_ago
        context['seven_days_ago'] = seven_days_ago

        return context

class StaffDashboardView(LoginRequiredMixin, UserPassesTestMixin, TemplateView):
    """
    Dashboard for technical staff with focus on assigned tasks and personal performance.
    """
    template_name = 'accounts/dashboards/staff_dashboard.html'

    def test_func(self):
        # Check if user is staff but not an admin
        return self.request.user.is_staff and not self.request.user.is_superuser

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user

        # User departments
        user_departments = user.departments.all()
        context['user_departments'] = user_departments

        department_ids = user_departments.values_list('department_id', flat=True)

        # Assigned tasks
        assigned_tasks = Task.objects.filter(
            assigned_to=user
        ).select_related('case', 'workflow_stage')

        context['assigned_tasks'] = assigned_tasks
        context['pending_tasks'] = assigned_tasks.filter(status='pending')
        context['in_progress_tasks'] = assigned_tasks.filter(status='in_progress')
        context['completed_tasks'] = assigned_tasks.filter(status='completed')

        # Task statistics
        total_assigned = assigned_tasks.count()
        total_completed = context['completed_tasks'].count()
        context['total_assigned'] = total_assigned
        context['total_completed'] = total_completed

        context['completion_rate'] = (
            (total_completed / total_assigned * 100)
            if total_assigned > 0 else 0
        )

        # Department tasks (tasks in user's departments)
        department_tasks = Task.objects.filter(
            workflow_stage__department_id__in=department_ids
        ).exclude(assigned_to=user).select_related(
            'case', 'assigned_to', 'workflow_stage'
        )
        context['department_tasks'] = department_tasks.order_by('-created_at')[:10]

        # Recent activity
        context['recent_activity'] = assigned_tasks.order_by('-updated_at')[:10]

        return context

# Helper dictionaries (assuming these exist or can be added to Task model)
# Add these to your Task model or define them here if needed for labels
Task.STATUS_CHOICES_DICT = dict(Task.STATUS_CHOICES)
Task.PRIORITY_CHOICES_DICT = dict(Task.PRIORITY_CHOICES)


# Route to appropriate dashboard based on user role
@login_required
def dashboard_router(request):
    """
    Routes users to their appropriate dashboard based on role.
    """
    user = request.user

    # Route based on user type
    if user.is_superuser:
        return redirect('accounts:admin_dashboard')
    elif user.is_staff:
        # Check if user is a department manager
        if user.managed_departments.exists():
            return redirect('accounts:manager_dashboard')
        else:
            return redirect('accounts:staff_dashboard')
    elif user.user_type == 2:  # Dentist
        return redirect('Dentists:dashboard')

    # Fallback to home if no specific dashboard
    return redirect('home')
