{% extends 'accounts/dashboards/dashboard_base.html' %}
{% load static %}
{% load i18n %}
{% load humanize %}
{% load dashboard_extras %}

{% block dashboard_title %}{% trans "Administrator Dashboard" %}{% endblock %}

{% block dashboard_header_title %}{% trans "Administrator Dashboard" %}{% endblock %}
{% block dashboard_header_subtitle %}
  {% trans "Complete system overview and management" %}
  <div class="header-controls" style="float: right; margin-top: -10px;">
    <select class="form-select form-select-sm" id="dateRangeSelect" onchange="updateDashboard()" style="display: inline-block; width: auto; margin-right: 10px;">
      <option value="7" {% if selected_range == "7" %}selected{% endif %}>Last 7 Days</option>
      <option value="30" {% if selected_range == "30" %}selected{% endif %}>Last 30 Days</option>
      <option value="90" {% if selected_range == "90" %}selected{% endif %}>Last 3 Months</option>
      <option value="180" {% if selected_range == "180" %}selected{% endif %}>Last 6 Months</option>
      <option value="365" {% if selected_range == "365" %}selected{% endif %}>Last Year</option>
    </select>
    <button class="btn btn-sm btn-primary" onclick="refreshDashboard()" title="Refresh Data">
      <i class="fas fa-sync-alt"></i>
    </button>
  </div>
{% endblock %}

{% block dashboard_quick_actions %}
<a href="{% url 'admin:index' %}" class="action-button">
  <i class="fas fa-cogs"></i> {% trans "Admin Panel" %}
</a>
<a href="{% url 'accounts:system_settings' %}" class="action-button">
  <i class="fas fa-sliders-h"></i> {% trans "System Settings" %}
</a>
<a href="{% url 'case:case_create' %}" class="action-button">
  <i class="fas fa-plus"></i> {% trans "New Case" %}
</a>
<a href="{% url 'billing:invoice_list' %}" class="action-button">
  <i class="fas fa-file-invoice-dollar"></i> {% trans "Invoices" %}
</a>
{% endblock %}

{% block dashboard_stats %}
<!-- User Statistics -->
<div class="stat-card primary">
  <div class="stat-title">{% trans "Total Users" %}</div>
  <div class="stat-value">{{ total_users|default:0|intcomma }}</div>
  <div class="stat-change">
    <i class="fas fa-users"></i> {{ active_users|default:0|intcomma }} {% trans "active" %} • {{ recent_users|default:0 }} {% trans "new this period" %}
  </div>
</div>

<div class="stat-card info">
  <div class="stat-title">{% trans "Staff Members" %}</div>
  <div class="stat-value">{{ staff_count|default:0|intcomma }}</div>
  <div class="stat-change">
    <i class="fas fa-user-tie"></i> {% trans "Technical team" %}
  </div>
</div>

<div class="stat-card success">
  <div class="stat-title">{% trans "Dentists" %}</div>
  <div class="stat-value">{{ dentist_count|default:0|intcomma }}</div>
  <div class="stat-change">
    <i class="fas fa-tooth"></i> {% trans "Registered clients" %}
  </div>
</div>

<!-- Case Statistics -->
<div class="stat-card warning">
  <div class="stat-title">{% trans "Total Cases" %}</div>
  <div class="stat-value">{{ total_cases|default:0|intcomma }}</div>
  <div class="stat-change">
    <i class="fas fa-{% if case_growth > 0 %}arrow-up{% elif case_growth < 0 %}arrow-down{% else %}minus{% endif %}"></i>
    {% if case_growth %}{{ case_growth|floatformat:1 }}%{% else %}0%{% endif %} {% trans "growth" %} • {{ overdue_cases|default:0 }} {% trans "overdue" %}
  </div>
</div>

<!-- Financial Statistics -->
<div class="stat-card {% if overdue_invoices > 0 %}danger{% else %}success{% endif %}">
  <div class="stat-title">{% trans "Revenue" %}</div>
  <div class="stat-value">${{ monthly_revenue|default:0|floatformat:0|intcomma }}</div>
  <div class="stat-change">
    <i class="fas fa-{% if revenue_growth > 0 %}arrow-up{% elif revenue_growth < 0 %}arrow-down{% else %}minus{% endif %}"></i>
    {% if revenue_growth %}{{ revenue_growth|floatformat:1 }}%{% else %}0%{% endif %} {% trans "vs last month" %} • {{ overdue_invoices|default:0 }} {% trans "overdue" %}
  </div>
</div>

<!-- Additional Statistics -->
<div class="stat-card info">
  <div class="stat-title">{% trans "Completion Rate" %}</div>
  <div class="stat-value">{{ on_time_delivery_rate|default:0|floatformat:1 }}%</div>
  <div class="stat-change">
    <i class="fas fa-clock"></i> {{ avg_completion_days|default:0|floatformat:1 }} {% trans "avg days" %}
  </div>
</div>
{% endblock %}

{% block dashboard_content %}
<!-- Charts Row -->
<div class="row">
  <!-- Case Volume Trends -->
  <div class="col-md-6 mb-4">
    <div class="dashboard-section">
      <div class="section-header">
        <h2 class="section-title">{% trans "Case Volume Trends" %}</h2>
      </div>
      <div class="section-body">
        <div style="height: 300px;">
          <canvas id="caseVolumeChart"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Department Workload -->
  <div class="col-md-6 mb-4">
    <div class="dashboard-section">
      <div class="section-header">
        <h2 class="section-title">{% trans "Department Workload" %}</h2>
      </div>
      <div class="section-body">
        <div style="height: 300px;">
          <canvas id="departmentWorkloadChart"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- Case Status Distribution -->
  <div class="col-md-6 mb-4">
    <div class="dashboard-section">
      <div class="section-header">
        <h2 class="section-title">{% trans "Case Status Distribution" %}</h2>
      </div>
      <div class="section-body">
        <div style="height: 300px;">
          <canvas id="caseStatusChart"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Invoice Status -->
  <div class="col-md-6 mb-4">
    <div class="dashboard-section">
      <div class="section-header">
        <h2 class="section-title">{% trans "Invoice Status" %}</h2>
      </div>
      <div class="section-body">
        <div style="height: 300px;">
          <canvas id="invoiceStatusChart"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Department Overview -->
<div class="dashboard-section">
  <div class="section-header">
    <h2 class="section-title">{% trans "Department Overview" %}</h2>
    <a href="#" class="btn btn-sm btn-outline-primary">{% trans "View All" %}</a>
  </div>
  <div class="section-body">
    <div class="table-responsive">
      <table class="data-table">
        <thead>
          <tr>
            <th>{% trans "Department" %}</th>
            <th>{% trans "Cases" %}</th>
            <th>{% trans "Tasks" %}</th>
            <th>{% trans "Actions" %}</th>
          </tr>
        </thead>
        <tbody>
          {% for dept in departments %}
          <tr>
            <td>{{ dept.name }}</td>
            <td>{{ dept.case_count }}</td>
            <td>{{ dept.task_count }}</td>
            <td>
              <a href="#" class="btn btn-sm btn-outline-primary">{% trans "Details" %}</a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="4" class="text-center">{% trans "No departments found" %}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Recent Cases -->
<div class="dashboard-section">
  <div class="section-header">
    <h2 class="section-title">{% trans "Recent Cases" %}</h2>
    <a href="{% url 'case:case_list' %}" class="btn btn-sm btn-outline-primary">{% trans "View All" %}</a>
  </div>
  <div class="section-body">
    <div class="table-responsive">
      <table class="data-table">
        <thead>
          <tr>
            <th>{% trans "Case #" %}</th>
            <th>{% trans "Patient" %}</th>
            <th>{% trans "Dentist" %}</th>
            <th>{% trans "Status" %}</th>
            <th>{% trans "Created" %}</th>
            <th>{% trans "Actions" %}</th>
          </tr>
        </thead>
        <tbody>
          {% for case in recent_cases %}
          <tr>
            <td><a href="{% url 'case:case_detail' case.case_number %}">{{ case.case_number }}</a></td>
            <td>{{ case.patient.get_full_name }}</td>
            <td>{{ case.dentist.user.get_full_name }}</td>
            <td>
              <span class="status-badge {{ case.status }}">{{ case.get_status_display }}</span>
            </td>
            <td>{{ case.created_at|date:"M d, Y" }}</td>
            <td>
              <a href="{% url 'case:case_detail' case.case_number %}" class="btn btn-sm btn-outline-primary">{% trans "View" %}</a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="6" class="text-center">{% trans "No recent cases found" %}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<!-- Recent Invoices -->
<div class="dashboard-section">
  <div class="section-header">
    <h2 class="section-title">{% trans "Recent Invoices" %}</h2>
    <a href="{% url 'billing:invoice_list' %}" class="btn btn-sm btn-outline-primary">{% trans "View All" %}</a>
  </div>
  <div class="section-body">
    <div class="table-responsive">
      <table class="data-table">
        <thead>
          <tr>
            <th>{% trans "Invoice #" %}</th>
            <th>{% trans "Dentist" %}</th>
            <th>{% trans "Amount" %}</th>
            <th>{% trans "Status" %}</th>
            <th>{% trans "Date" %}</th>
            <th>{% trans "Actions" %}</th>
          </tr>
        </thead>
        <tbody>
          {% for invoice in recent_invoices %}
          <tr>
            <td><a href="{% url 'billing:invoice_detail' invoice.id %}">{{ invoice.invoice_number }}</a></td>
            <td>{{ invoice.dentist.user.get_full_name }}</td>
            <td>{{ invoice.total_amount }} {{ invoice.currency.code }}</td>
            <td>
              <span class="status-badge {{ invoice.status }}">{{ invoice.get_status_display }}</span>
            </td>
            <td>{{ invoice.created_at|date:"M d, Y" }}</td>
            <td>
              <a href="{% url 'billing:invoice_detail' invoice.id %}" class="btn btn-sm btn-outline-primary">{% trans "View" %}</a>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="6" class="text-center">{% trans "No recent invoices found" %}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>
{% endblock %}

{% block dashboard_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Case Volume Trends Chart with real data
    const caseVolumeCtx = document.getElementById('caseVolumeChart').getContext('2d');

    // Get chart data from backend
    const caseVolumeData = {{ chart_data.case_volume_trends|safe|default:"[]" }};

    new Chart(caseVolumeCtx, {
      type: 'line',
      data: {
        labels: caseVolumeData.map(item => item.month),
        datasets: [{
          label: '{% trans "Cases" %}',
          data: caseVolumeData.map(item => item.cases),
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          borderWidth: 2,
          tension: 0.3,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        },
        plugins: {
          legend: {
            display: true,
            position: 'top'
          }
        }
      }
    });

    // Department Workload Chart with real data
    const departmentWorkloadCtx = document.getElementById('departmentWorkloadChart').getContext('2d');

    // Get department data from backend
    const departmentData = {{ chart_data.department_workload|safe|default:"[]" }};

    new Chart(departmentWorkloadCtx, {
      type: 'bar',
      data: {
        labels: departmentData.map(dept => dept.name),
        datasets: [{
          label: '{% trans "Total Cases" %}',
          data: departmentData.map(dept => dept.total_cases),
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }, {
          label: '{% trans "Active Cases" %}',
          data: departmentData.map(dept => dept.active_cases),
          backgroundColor: 'rgba(255, 159, 64, 0.7)',
          borderColor: 'rgba(255, 159, 64, 1)',
          borderWidth: 1
        }, {
          label: '{% trans "Capacity" %}',
          data: departmentData.map(dept => dept.capacity),
          backgroundColor: 'rgba(75, 192, 192, 0.7)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
          type: 'line'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        },
        plugins: {
          legend: {
            display: true,
            position: 'top'
          }
        }
      }
    });

    // Case Status Distribution Chart
    const caseStatusCtx = document.getElementById('caseStatusChart').getContext('2d');
    new Chart(caseStatusCtx, {
      type: 'doughnut',
      data: {
        labels: ['{% trans "Pending" %}', '{% trans "In Progress" %}', '{% trans "Completed" %}', '{% trans "Cancelled" %}'],
        datasets: [{
          data: [
            {{ total_cases|divide:4|floatformat:0 }},
            {{ active_cases }},
            {{ total_cases|subtract:active_cases|multiply:0.7|floatformat:0 }},
            {{ total_cases|subtract:active_cases|multiply:0.3|floatformat:0 }}
          ],
          backgroundColor: [
            'rgba(255, 206, 86, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(255, 99, 132, 0.7)'
          ],
          borderColor: [
            'rgba(255, 206, 86, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(255, 99, 132, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right'
          }
        }
      }
    });

    // Invoice Status Chart
    const invoiceStatusCtx = document.getElementById('invoiceStatusChart').getContext('2d');
    new Chart(invoiceStatusCtx, {
      type: 'pie',
      data: {
        labels: ['{% trans "Paid" %}', '{% trans "Unpaid" %}', '{% trans "Overdue" %}'],
        datasets: [{
          data: [
            {{ total_invoices|subtract:unpaid_invoices }},
            {{ unpaid_invoices|multiply:0.7|floatformat:0 }},
            {{ unpaid_invoices|multiply:0.3|floatformat:0 }}
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(255, 99, 132, 0.7)'
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(255, 99, 132, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right'
          }
        }
      }
    });
  });

  // Dashboard control functions
  function updateDashboard() {
    const range = document.getElementById('dateRangeSelect').value;
    const url = new URL(window.location);
    url.searchParams.set('range', range);
    window.location.href = url.toString();
  }

  function refreshDashboard() {
    window.location.reload();
  }

  // Add loading states
  function showLoading() {
    document.body.style.cursor = 'wait';
  }

  function hideLoading() {
    document.body.style.cursor = 'default';
  }

  // Initialize dashboard
  console.log('Admin Dashboard initialized with real database data');
  console.log('Chart data available:', {
    caseVolume: {{ chart_data.case_volume_trends|length|default:0 }},
    departments: {{ chart_data.department_workload|length|default:0 }},
    statusDistribution: {{ chart_data.case_status_distribution|length|default:0 }}
  });
</script>
{% endblock %}
